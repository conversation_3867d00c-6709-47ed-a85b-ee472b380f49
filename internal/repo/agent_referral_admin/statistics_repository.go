package agent_referral_admin

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral_admin"
)

// StatisticsRepository implements StatisticsRepositoryInterface
type StatisticsRepository struct {
	db *gorm.DB
}

// NewStatisticsRepository creates a new StatisticsRepository
func NewStatisticsRepository() StatisticsRepositoryInterface {
	return &StatisticsRepository{
		db: global.GVA_DB,
	}
}

func (r *StatisticsRepository) GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (*agent_referral_admin.AgentReferralStatsResult, error) {
	stats := &agent_referral_admin.AgentReferralStatsResult{}

	// Get total users
	var totalUsers int64
	if err := r.db.WithContext(ctx).Model(&model.User{}).Count(&totalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}
	stats.TotalUsers = int(totalUsers)

	// Get total active users (users with first_transaction_at not null)
	var totalActiveUsers int64
	if err := r.db.WithContext(ctx).Model(&model.User{}).
		Where("first_transaction_at IS NOT NULL").Count(&totalActiveUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count active users: %w", err)
	}
	stats.TotalActiveUsers = int(totalActiveUsers)

	// Get total trading users (same as active users for now)
	stats.TotalTradingUsers = stats.TotalActiveUsers

	// Get total infinite agents
	var totalInfiniteAgents int64
	if err := r.db.WithContext(ctx).Model(&model.InfiniteAgentConfig{}).
		Where("status = 'ACTIVE'").Count(&totalInfiniteAgents).Error; err != nil {
		return nil, fmt.Errorf("failed to count infinite agents: %w", err)
	}
	stats.TotalInfiniteAgents = int(totalInfiniteAgents)

	// Get total referral trees
	var totalReferralTrees int64
	if err := r.db.WithContext(ctx).Model(&model.ReferralTreeSnapshot{}).
		Where("is_valid = true").Count(&totalReferralTrees).Error; err != nil {
		return nil, fmt.Errorf("failed to count referral trees: %w", err)
	}
	stats.TotalReferralTrees = int(totalReferralTrees)

	// Calculate total volume and commission from referral snapshots
	var volumeCommissionStats struct {
		TotalVolumeUSD         float64
		TotalCommissionPaidUSD float64
		TotalCashbackPaidUSD   float64
	}

	if err := r.db.WithContext(ctx).Model(&model.ReferralSnapshot{}).
		Select(`
			COALESCE(SUM(total_perps_volume_usd + total_meme_volume_usd), 0) as total_volume_usd,
			COALESCE(SUM(total_commission_earned_usd), 0) as total_commission_paid_usd,
			COALESCE(SUM(total_cashback_earned_usd), 0) as total_cashback_paid_usd
		`).Scan(&volumeCommissionStats).Error; err != nil {
		global.GVA_LOG.Warn("Failed to calculate volume and commission stats", zap.Error(err))
	}

	stats.TotalVolumeUSD = volumeCommissionStats.TotalVolumeUSD
	stats.TotalCommissionPaidUSD = volumeCommissionStats.TotalCommissionPaidUSD
	stats.TotalCashbackPaidUSD = volumeCommissionStats.TotalCashbackPaidUSD

	// Calculate average tree depth and direct referrals
	var avgStats struct {
		AverageTreeDepth       float64
		AverageDirectReferrals float64
	}

	if err := r.db.WithContext(ctx).Model(&model.ReferralTreeSnapshot{}).
		Select(`
			COALESCE(AVG(max_depth), 0) as average_tree_depth,
			COALESCE(AVG(direct_count), 0) as average_direct_referrals
		`).Where("is_valid = true").Scan(&avgStats).Error; err != nil {
		global.GVA_LOG.Warn("Failed to calculate average stats", zap.Error(err))
	}

	stats.AverageTreeDepth = avgStats.AverageTreeDepth
	stats.AverageDirectReferrals = avgStats.AverageDirectReferrals

	// Get level distribution
	levelDistribution, err := r.getLevelDistribution(ctx)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get level distribution", zap.Error(err))
		levelDistribution = []*agent_referral_admin.LevelDistribution{}
	}
	stats.LevelDistribution = levelDistribution

	// Get daily stats (placeholder for now)
	stats.DailyStats = []*agent_referral_admin.DailyAgentStats{}

	return stats, nil
}

func (r *StatisticsRepository) GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (*agent_referral_admin.CommissionDistributionResult, error) {
	// TODO: Implement commission distribution statistics
	// This would involve querying commission ledger tables and calculating distributions
	
	return &agent_referral_admin.CommissionDistributionResult{
		TotalCommissionPaidUSD:     0,
		DirectCommissionUSD:        0,
		IndirectCommissionUSD:      0,
		ExtendedCommissionUSD:      0,
		InfiniteAgentCommissionUSD: 0,
		LevelBreakdown:             []*agent_referral_admin.CommissionLevelBreakdown{},
		DailyDistribution:          []*agent_referral_admin.DailyCommissionStats{},
	}, nil
}

func (r *StatisticsRepository) GetTopPerformingAgents(ctx context.Context, input *agent_referral_admin.GetTopAgentsInput) (*agent_referral_admin.TopAgentsResult, error) {
	var snapshots []model.ReferralSnapshot

	query := r.db.WithContext(ctx).Preload("User").Preload("User.AgentLevel")

	// Apply filters
	if input.AgentLevelID != nil {
		query = query.Joins("JOIN users ON referral_snapshots.user_id = users.id").
			Where("users.agent_level_id = ?", *input.AgentLevelID)
	}

	// Apply sorting
	orderClause := "total_commission_earned_usd DESC"
	if input.SortBy == "VOLUME_USD" {
		orderClause = "(total_perps_volume_usd + total_meme_volume_usd) DESC"
	}

	if err := query.Order(orderClause).Limit(input.Limit).Find(&snapshots).Error; err != nil {
		global.GVA_LOG.Error("Failed to get top performing agents", zap.Error(err))
		return nil, fmt.Errorf("failed to get top performing agents: %w", err)
	}

	var agents []*agent_referral_admin.TopAgent
	for i, snapshot := range snapshots {
		// Check if user is infinite agent
		var isInfiniteAgent bool
		var infiniteAgentCount int64
		r.db.WithContext(ctx).Model(&model.InfiniteAgentConfig{}).
			Where("user_id = ? AND status = 'ACTIVE'", snapshot.UserID).Count(&infiniteAgentCount)
		isInfiniteAgent = infiniteAgentCount > 0

		agent := &agent_referral_admin.TopAgent{
			UserID:                   snapshot.UserID,
			User:                     &snapshot.User,
			AgentLevelID:             int(snapshot.User.AgentLevelID),
			AgentLevel:               &snapshot.User.AgentLevel,
			TotalVolumeUSD:           float64(snapshot.TotalPerpsVolumeUSD.InexactFloat64() + snapshot.TotalMemeVolumeUSD.InexactFloat64()),
			TotalCommissionEarnedUSD: float64(snapshot.TotalCommissionEarnedUSD.InexactFloat64()),
			DirectReferrals:          snapshot.DirectCount,
			TotalDownline:            snapshot.TotalDownlineCount,
			TradingUsers:             snapshot.TradingUserCount,
			Rank:                     i + 1,
			IsInfiniteAgent:          isInfiniteAgent,
		}
		agents = append(agents, agent)
	}

	return &agent_referral_admin.TopAgentsResult{
		Agents:     agents,
		TotalCount: len(agents),
	}, nil
}

func (r *StatisticsRepository) GetInfiniteAgentPerformanceStats(ctx context.Context, input *agent_referral_admin.GetInfiniteAgentStatsInput) (*agent_referral_admin.InfiniteAgentPerformanceResult, error) {
	// TODO: Implement infinite agent performance statistics
	// This would involve complex calculations based on infinite agent trees and commissions
	
	return &agent_referral_admin.InfiniteAgentPerformanceResult{
		InfiniteAgentUserID:            input.InfiniteAgentUserID,
		TotalCommissionEarnedUSD:       0,
		TotalNetFeeUSD:                 0,
		TotalStandardCommissionPaidUSD: 0,
		FinalCommissionAmountUSD:       0,
		CommissionRateN:                0,
		TreeCount:                      0,
		TotalNodes:                     0,
		ActiveUsers:                    0,
		TradingUsers:                   0,
		TotalVolumeUSD:                 0,
		MemeBreakdown:                  &agent_referral_admin.InfiniteAgentMemeBreakdown{},
		ContractBreakdown:              &agent_referral_admin.InfiniteAgentContractBreakdown{},
		DailyPerformance:               []*agent_referral_admin.DailyInfiniteAgentStats{},
		TreeBreakdown:                  []*agent_referral_admin.InfiniteAgentTreeBreakdown{},
	}, nil
}

func (r *StatisticsRepository) RecalculateAllReferralSnapshots(ctx context.Context) (*agent_referral_admin.SystemOperationResult, error) {
	startTime := time.Now()
	
	// TODO: Implement logic to recalculate all referral snapshots
	// This would involve iterating through all users and recalculating their snapshots
	
	duration := time.Since(startTime)
	
	return &agent_referral_admin.SystemOperationResult{
		ProcessedCount: 0,
		ErrorCount:     0,
		Duration:       duration.String(),
	}, nil
}

func (r *StatisticsRepository) RecalculateAllInfiniteAgentCommissions(ctx context.Context) (*agent_referral_admin.SystemOperationResult, error) {
	startTime := time.Now()
	
	// TODO: Implement logic to recalculate all infinite agent commissions
	// This would involve iterating through all infinite agents and recalculating their commissions
	
	duration := time.Since(startTime)
	
	return &agent_referral_admin.SystemOperationResult{
		ProcessedCount: 0,
		ErrorCount:     0,
		Duration:       duration.String(),
	}, nil
}

func (r *StatisticsRepository) SyncReferralTreeData(ctx context.Context) (*agent_referral_admin.SystemOperationResult, error) {
	startTime := time.Now()
	
	// TODO: Implement logic to sync referral tree data
	// This would involve synchronizing data between different referral-related tables
	
	duration := time.Since(startTime)
	
	return &agent_referral_admin.SystemOperationResult{
		ProcessedCount: 0,
		ErrorCount:     0,
		Duration:       duration.String(),
	}, nil
}

// Helper functions

func (r *StatisticsRepository) getLevelDistribution(ctx context.Context) ([]*agent_referral_admin.LevelDistribution, error) {
	var results []struct {
		LevelID                int     `json:"level_id"`
		LevelName              string  `json:"level_name"`
		UserCount              int     `json:"user_count"`
		TotalVolumeUSD         float64 `json:"total_volume_usd"`
		TotalCommissionPaidUSD float64 `json:"total_commission_paid_usd"`
	}

	if err := r.db.WithContext(ctx).Raw(`
		SELECT 
			al.id as level_id,
			al.name as level_name,
			COUNT(u.id) as user_count,
			COALESCE(SUM(rs.total_perps_volume_usd + rs.total_meme_volume_usd), 0) as total_volume_usd,
			COALESCE(SUM(rs.total_commission_earned_usd), 0) as total_commission_paid_usd
		FROM agent_levels al
		LEFT JOIN users u ON al.id = u.agent_level_id
		LEFT JOIN referral_snapshots rs ON u.id = rs.user_id
		GROUP BY al.id, al.name
		ORDER BY al.id
	`).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get level distribution: %w", err)
	}

	// Calculate total users for percentage calculation
	var totalUsers int
	for _, result := range results {
		totalUsers += result.UserCount
	}

	var distribution []*agent_referral_admin.LevelDistribution
	for _, result := range results {
		percentage := 0.0
		if totalUsers > 0 {
			percentage = float64(result.UserCount) / float64(totalUsers) * 100
		}

		distribution = append(distribution, &agent_referral_admin.LevelDistribution{
			LevelID:                result.LevelID,
			LevelName:              result.LevelName,
			UserCount:              result.UserCount,
			Percentage:             percentage,
			TotalVolumeUSD:         result.TotalVolumeUSD,
			TotalCommissionPaidUSD: result.TotalCommissionPaidUSD,
		})
	}

	return distribution, nil
}
