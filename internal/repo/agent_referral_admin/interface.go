package agent_referral_admin

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral_admin"
)

// InfiniteAgentRepositoryInterface defines the interface for infinite agent repository operations
type InfiniteAgentRepositoryInterface interface {
	// GetAllWithPagination retrieves all infinite agents with pagination
	GetAllWithPagination(ctx context.Context, page, pageSize int, sortBy, sortOrder string) ([]*model.InfiniteAgentConfig, int, error)
	
	// GetByID retrieves infinite agent by ID
	GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	
	// GetByUserID retrieves infinite agent by user ID
	GetByUserID(ctx context.Context, userId uuid.UUID) (*model.InfiniteAgentConfig, error)
	
	// Create creates a new infinite agent
	Create(ctx context.Context, agent *model.InfiniteAgentConfig) error
	
	// Update updates an existing infinite agent
	Update(ctx context.Context, agent *model.InfiniteAgentConfig) error
	
	// Delete deletes an infinite agent
	Delete(ctx context.Context, id uuid.UUID) error
}

// AgentLevelRepositoryInterface defines the interface for agent level repository operations
type AgentLevelRepositoryInterface interface {
	// GetAllWithStats retrieves all agent levels with statistics
	GetAllWithStats(ctx context.Context) ([]*agent_referral_admin.AgentLevelWithStats, error)
	
	// GetByIDWithStats retrieves agent level by ID with statistics
	GetByIDWithStats(ctx context.Context, id int) (*agent_referral_admin.AgentLevelWithStats, error)
	
	// GetByID retrieves agent level by ID
	GetByID(ctx context.Context, id int) (*model.AgentLevel, error)
	
	// Update updates an agent level
	Update(ctx context.Context, level *model.AgentLevel) error
}

// ReferralTreeRepositoryInterface defines the interface for referral tree repository operations
type ReferralTreeRepositoryInterface interface {
	// GetAllWithPagination retrieves all referral trees with pagination
	GetAllWithPagination(ctx context.Context, input *agent_referral_admin.GetReferralTreesInput) ([]*agent_referral_admin.ReferralTreeWithNodes, int, error)
	
	// GetByIDWithNodes retrieves referral tree by ID with nodes
	GetByIDWithNodes(ctx context.Context, id uint) (*agent_referral_admin.ReferralTreeWithNodes, error)
	
	// GetByRootUserWithNodes retrieves referral tree by root user with nodes
	GetByRootUserWithNodes(ctx context.Context, rootUserId uuid.UUID) (*agent_referral_admin.ReferralTreeWithNodes, error)
	
	// CreateSnapshot creates a new referral tree snapshot
	CreateSnapshot(ctx context.Context, input *agent_referral_admin.CreateTreeSnapshotInput) (*agent_referral_admin.CreateTreeSnapshotResult, error)
	
	// CreateAllSnapshots creates snapshots for all root users
	CreateAllSnapshots(ctx context.Context) (*agent_referral_admin.CreateAllTreeSnapshotsResult, error)
	
	// GetInfiniteAgentTreesWithPagination retrieves infinite agent referral trees with pagination
	GetInfiniteAgentTreesWithPagination(ctx context.Context, input *agent_referral_admin.GetInfiniteAgentTreesInput) ([]*model.InfiniteAgentReferralTree, int, error)
	
	// CreateInfiniteAgentTrees creates infinite agent referral trees
	CreateInfiniteAgentTrees(ctx context.Context, input *agent_referral_admin.CreateInfiniteAgentTreesInput) (*agent_referral_admin.CreateInfiniteAgentTreesResult, error)
	
	// RecalculateStats recalculates statistics for a referral tree
	RecalculateStats(ctx context.Context, treeId uint) (*agent_referral_admin.ReferralTreeWithNodes, error)
}

// UserRepositoryInterface defines the interface for user repository operations
type UserRepositoryInterface interface {
	// SearchByInvitationCode searches users by invitation code
	SearchByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error)
	
	// GetReferralSnapshot retrieves user referral snapshot
	GetReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error)
	
	// GetReferralTree retrieves user referral tree with specified depth
	GetReferralTree(ctx context.Context, userId uuid.UUID, depth int) (*agent_referral_admin.UserReferralTreeResult, error)
	
	// UpdateAgentLevel updates user agent level
	UpdateAgentLevel(ctx context.Context, input *agent_referral_admin.UpdateUserAgentLevelInput) (*model.User, error)
	
	// RecalculateReferralSnapshot recalculates user referral snapshot
	RecalculateReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error)
	
	// CreateReferralRelationship creates a referral relationship
	CreateReferralRelationship(ctx context.Context, input *agent_referral_admin.CreateReferralInput) (*agent_referral_admin.CreateReferralResult, error)
	
	// DeleteReferralRelationship deletes a referral relationship
	DeleteReferralRelationship(ctx context.Context, input *agent_referral_admin.DeleteReferralInput) error
}

// StatisticsRepositoryInterface defines the interface for statistics repository operations
type StatisticsRepositoryInterface interface {
	// GetAgentReferralStats retrieves overall agent referral statistics
	GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (*agent_referral_admin.AgentReferralStatsResult, error)
	
	// GetCommissionDistributionStats retrieves commission distribution statistics
	GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (*agent_referral_admin.CommissionDistributionResult, error)
	
	// GetTopPerformingAgents retrieves top performing agents
	GetTopPerformingAgents(ctx context.Context, input *agent_referral_admin.GetTopAgentsInput) (*agent_referral_admin.TopAgentsResult, error)
	
	// GetInfiniteAgentPerformanceStats retrieves infinite agent performance statistics
	GetInfiniteAgentPerformanceStats(ctx context.Context, input *agent_referral_admin.GetInfiniteAgentStatsInput) (*agent_referral_admin.InfiniteAgentPerformanceResult, error)
	
	// RecalculateAllReferralSnapshots recalculates all user referral snapshots
	RecalculateAllReferralSnapshots(ctx context.Context) (*agent_referral_admin.SystemOperationResult, error)
	
	// RecalculateAllInfiniteAgentCommissions recalculates all infinite agent commissions
	RecalculateAllInfiniteAgentCommissions(ctx context.Context) (*agent_referral_admin.SystemOperationResult, error)
	
	// SyncReferralTreeData synchronizes referral tree data
	SyncReferralTreeData(ctx context.Context) (*agent_referral_admin.SystemOperationResult, error)
}
