package agent_referral_admin

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral_admin"
)

// ReferralTreeRepository implements ReferralTreeRepositoryInterface
type ReferralTreeRepository struct {
	db *gorm.DB
}

// NewReferralTreeRepository creates a new ReferralTreeRepository
func NewReferralTreeRepository() ReferralTreeRepositoryInterface {
	return &ReferralTreeRepository{
		db: global.GVA_DB,
	}
}

func (r *ReferralTreeRepository) GetAllWithPagination(ctx context.Context, input *agent_referral_admin.GetReferralTreesInput) ([]*agent_referral_admin.ReferralTreeWithNodes, int, error) {
	var trees []model.ReferralTreeSnapshot
	var totalCount int64

	// Build base query
	query := r.db.WithContext(ctx).Model(&model.ReferralTreeSnapshot{})

	// Apply filters
	if input.RootUserID != nil {
		query = query.Where("root_user_id = ?", *input.RootUserID)
	}
	if input.HasInfiniteAgent != nil {
		query = query.Where("has_infinite_agent = ?", *input.HasInfiniteAgent)
	}
	if input.StartDate != nil {
		query = query.Where("snapshot_date >= ?", *input.StartDate)
	}
	if input.EndDate != nil {
		query = query.Where("snapshot_date <= ?", *input.EndDate)
	}

	// Count total records
	if err := query.Count(&totalCount).Error; err != nil {
		global.GVA_LOG.Error("Failed to count referral trees", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to count referral trees: %w", err)
	}

	// Apply sorting
	orderClause := "created_at DESC"
	if input.SortBy != "" {
		orderClause = fmt.Sprintf("%s %s", input.SortBy, input.SortOrder)
	}
	query = query.Order(orderClause)

	// Apply pagination
	offset := (input.Page - 1) * input.PageSize
	if err := query.Preload("RootUser").Preload("InfiniteAgentUser").
		Offset(offset).Limit(input.PageSize).Find(&trees).Error; err != nil {
		global.GVA_LOG.Error("Failed to get referral trees with pagination", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to get referral trees with pagination: %w", err)
	}

	// Convert to result format with nodes
	var result []*agent_referral_admin.ReferralTreeWithNodes
	for _, tree := range trees {
		treeWithNodes, err := r.loadTreeNodes(ctx, &tree)
		if err != nil {
			global.GVA_LOG.Warn("Failed to load nodes for tree", zap.Uint("treeId", tree.ID), zap.Error(err))
			treeWithNodes = &agent_referral_admin.ReferralTreeWithNodes{
				ReferralTreeSnapshot: &tree,
				Nodes:                []*model.ReferralTreeNode{},
			}
		}
		result = append(result, treeWithNodes)
	}

	return result, int(totalCount), nil
}

func (r *ReferralTreeRepository) GetByIDWithNodes(ctx context.Context, id uint) (*agent_referral_admin.ReferralTreeWithNodes, error) {
	var tree model.ReferralTreeSnapshot
	if err := r.db.WithContext(ctx).Preload("RootUser").Preload("InfiniteAgentUser").
		Where("id = ?", id).First(&tree).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("referral tree not found")
		}
		global.GVA_LOG.Error("Failed to get referral tree by ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get referral tree by ID: %w", err)
	}

	return r.loadTreeNodes(ctx, &tree)
}

func (r *ReferralTreeRepository) GetByRootUserWithNodes(ctx context.Context, rootUserId uuid.UUID) (*agent_referral_admin.ReferralTreeWithNodes, error) {
	var tree model.ReferralTreeSnapshot
	if err := r.db.WithContext(ctx).Preload("RootUser").Preload("InfiniteAgentUser").
		Where("root_user_id = ?", rootUserId).Order("created_at DESC").First(&tree).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("referral tree not found for root user")
		}
		global.GVA_LOG.Error("Failed to get referral tree by root user", zap.Error(err))
		return nil, fmt.Errorf("failed to get referral tree by root user: %w", err)
	}

	return r.loadTreeNodes(ctx, &tree)
}

func (r *ReferralTreeRepository) CreateSnapshot(ctx context.Context, input *agent_referral_admin.CreateTreeSnapshotInput) (*agent_referral_admin.CreateTreeSnapshotResult, error) {
	snapshotDate := time.Now().UTC()
	if input.SnapshotDate != nil {
		snapshotDate = *input.SnapshotDate
	}

	description := fmt.Sprintf("Admin created referral tree snapshot for user %s", input.RootUserID.String())
	if input.Description != nil {
		description = *input.Description
	}

	// Create tree snapshot
	tree := &model.ReferralTreeSnapshot{
		RootUserID:   input.RootUserID,
		SnapshotDate: snapshotDate,
		Description:  description,
		IsValid:      true,
	}

	if err := r.db.WithContext(ctx).Create(tree).Error; err != nil {
		global.GVA_LOG.Error("Failed to create referral tree snapshot", zap.Error(err))
		return nil, fmt.Errorf("failed to create referral tree snapshot: %w", err)
	}

	// TODO: Implement logic to create tree nodes based on referral relationships
	// This would involve querying the referrals table and building the tree structure
	processedNodes := 0

	return &agent_referral_admin.CreateTreeSnapshotResult{
		TreeID:         tree.ID,
		ProcessedNodes: processedNodes,
	}, nil
}

func (r *ReferralTreeRepository) CreateAllSnapshots(ctx context.Context) (*agent_referral_admin.CreateAllTreeSnapshotsResult, error) {
	// TODO: Implement logic to create snapshots for all root users
	// This would involve finding all root users and creating snapshots for each
	
	return &agent_referral_admin.CreateAllTreeSnapshotsResult{
		ProcessedTrees: 0,
		ErrorCount:     0,
		TotalNodes:     0,
	}, nil
}

func (r *ReferralTreeRepository) GetInfiniteAgentTreesWithPagination(ctx context.Context, input *agent_referral_admin.GetInfiniteAgentTreesInput) ([]*model.InfiniteAgentReferralTree, int, error) {
	var trees []*model.InfiniteAgentReferralTree
	var totalCount int64

	// Build base query
	query := r.db.WithContext(ctx).Model(&model.InfiniteAgentReferralTree{})

	// Apply filters
	if input.InfiniteAgentUserID != nil {
		query = query.Where("infinite_agent_user_id = ?", *input.InfiniteAgentUserID)
	}
	if input.StartDate != nil {
		query = query.Where("snapshot_date >= ?", *input.StartDate)
	}
	if input.EndDate != nil {
		query = query.Where("snapshot_date <= ?", *input.EndDate)
	}

	// Count total records
	if err := query.Count(&totalCount).Error; err != nil {
		global.GVA_LOG.Error("Failed to count infinite agent trees", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to count infinite agent trees: %w", err)
	}

	// Apply sorting
	orderClause := "created_at DESC"
	if input.SortBy != "" {
		orderClause = fmt.Sprintf("%s %s", input.SortBy, input.SortOrder)
	}
	query = query.Order(orderClause)

	// Apply pagination
	offset := (input.Page - 1) * input.PageSize
	if err := query.Preload("InfiniteAgentUser").Preload("RootUser").
		Offset(offset).Limit(input.PageSize).Find(&trees).Error; err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent trees with pagination", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to get infinite agent trees with pagination: %w", err)
	}

	return trees, int(totalCount), nil
}

func (r *ReferralTreeRepository) CreateInfiniteAgentTrees(ctx context.Context, input *agent_referral_admin.CreateInfiniteAgentTreesInput) (*agent_referral_admin.CreateInfiniteAgentTreesResult, error) {
	// TODO: Implement logic to create infinite agent referral trees
	// This would involve processing each infinite agent and creating their tree structures
	
	snapshotDate := time.Now().UTC()
	if input.SnapshotDate != nil {
		snapshotDate = *input.SnapshotDate
	}

	return &agent_referral_admin.CreateInfiniteAgentTreesResult{
		ProcessedCount:        0,
		ErrorCount:            0,
		TotalInfiniteAgents:   len(input.InfiniteAgentUserIDs),
		SnapshotDate:          snapshotDate.Format("2006-01-02"),
	}, nil
}

func (r *ReferralTreeRepository) RecalculateStats(ctx context.Context, treeId uint) (*agent_referral_admin.ReferralTreeWithNodes, error) {
	// Get the tree
	tree, err := r.GetByIDWithNodes(ctx, treeId)
	if err != nil {
		return nil, err
	}

	// TODO: Implement logic to recalculate tree statistics
	// This would involve recalculating totalNodes, maxDepth, directCount, activeUsers, tradingUsers, etc.

	return tree, nil
}

func (r *ReferralTreeRepository) loadTreeNodes(ctx context.Context, tree *model.ReferralTreeSnapshot) (*agent_referral_admin.ReferralTreeWithNodes, error) {
	var nodes []*model.ReferralTreeNode
	if err := r.db.WithContext(ctx).
		Preload("User").
		Preload("ParentUser").
		Preload("Referrer").
		Preload("AgentLevel").
		Where("tree_snapshot_id = ?", tree.ID).
		Order("depth ASC, position ASC").
		Find(&nodes).Error; err != nil {
		global.GVA_LOG.Error("Failed to load tree nodes", zap.Error(err))
		return nil, fmt.Errorf("failed to load tree nodes: %w", err)
	}

	// Calculate additional statistics
	totalVolumeUSD := 0.0
	totalCommissionPaidUSD := 0.0

	// TODO: Calculate volume and commission from nodes
	// This would involve querying transaction data for each node

	return &agent_referral_admin.ReferralTreeWithNodes{
		ReferralTreeSnapshot:   tree,
		Nodes:                  nodes,
		TotalVolumeUSD:         totalVolumeUSD,
		TotalCommissionPaidUSD: totalCommissionPaidUSD,
	}, nil
}
