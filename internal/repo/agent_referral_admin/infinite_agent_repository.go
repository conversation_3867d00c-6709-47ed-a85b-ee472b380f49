package agent_referral_admin

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// InfiniteAgentRepository implements InfiniteAgentRepositoryInterface
type InfiniteAgentRepository struct {
	db *gorm.DB
}

// NewInfiniteAgentRepository creates a new InfiniteAgentRepository
func NewInfiniteAgentRepository() InfiniteAgentRepositoryInterface {
	return &InfiniteAgentRepository{
		db: global.GVA_DB,
	}
}

func (r *InfiniteAgentRepository) GetAllWithPagination(ctx context.Context, page, pageSize int, sortBy, sortOrder string) ([]*model.InfiniteAgentConfig, int, error) {
	var agents []*model.InfiniteAgentConfig
	var totalCount int64

	// Count total records
	if err := r.db.WithContext(ctx).Model(&model.InfiniteAgentConfig{}).Count(&totalCount).Error; err != nil {
		global.GVA_LOG.Error("Failed to count infinite agents", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to count infinite agents: %w", err)
	}

	// Build query with pagination and sorting
	query := r.db.WithContext(ctx).Preload("User")

	// Apply sorting
	if sortBy != "" {
		orderClause := fmt.Sprintf("%s %s", sortBy, sortOrder)
		query = query.Order(orderClause)
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&agents).Error; err != nil {
		global.GVA_LOG.Error("Failed to get infinite agents with pagination", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to get infinite agents with pagination: %w", err)
	}

	return agents, int(totalCount), nil
}

func (r *InfiniteAgentRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	var agent model.InfiniteAgentConfig

	if err := r.db.WithContext(ctx).Preload("User").Where("id = ?", id).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("infinite agent not found")
		}
		global.GVA_LOG.Error("Failed to get infinite agent by ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent by ID: %w", err)
	}

	return &agent, nil
}

func (r *InfiniteAgentRepository) GetByUserID(ctx context.Context, userId uuid.UUID) (*model.InfiniteAgentConfig, error) {
	var agent model.InfiniteAgentConfig

	if err := r.db.WithContext(ctx).Preload("User").Where("user_id = ?", userId).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("infinite agent not found for user")
		}
		global.GVA_LOG.Error("Failed to get infinite agent by user ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent by user ID: %w", err)
	}

	return &agent, nil
}

func (r *InfiniteAgentRepository) Create(ctx context.Context, agent *model.InfiniteAgentConfig) error {
	if err := r.db.WithContext(ctx).Create(agent).Error; err != nil {
		global.GVA_LOG.Error("Failed to create infinite agent", zap.Error(err))
		return fmt.Errorf("failed to create infinite agent: %w", err)
	}

	// Load the user relationship
	if err := r.db.WithContext(ctx).Preload("User").Where("id = ?", agent.ID).First(agent).Error; err != nil {
		global.GVA_LOG.Warn("Failed to load user relationship after creating infinite agent", zap.Error(err))
	}

	return nil
}

func (r *InfiniteAgentRepository) Update(ctx context.Context, agent *model.InfiniteAgentConfig) error {
	if err := r.db.WithContext(ctx).Save(agent).Error; err != nil {
		global.GVA_LOG.Error("Failed to update infinite agent", zap.Error(err))
		return fmt.Errorf("failed to update infinite agent: %w", err)
	}

	// Load the user relationship
	if err := r.db.WithContext(ctx).Preload("User").Where("id = ?", agent.ID).First(agent).Error; err != nil {
		global.GVA_LOG.Warn("Failed to load user relationship after updating infinite agent", zap.Error(err))
	}

	return nil
}

func (r *InfiniteAgentRepository) Delete(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Delete(&model.InfiniteAgentConfig{}, "id = ?", id)
	if result.Error != nil {
		global.GVA_LOG.Error("Failed to delete infinite agent", zap.Error(result.Error))
		return fmt.Errorf("failed to delete infinite agent: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("infinite agent not found")
	}

	return nil
}
