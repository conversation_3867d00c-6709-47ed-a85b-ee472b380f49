package agent_referral_admin

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral_admin"
)

// UserRepository implements UserRepositoryInterface
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new UserRepository
func NewUserRepository() UserRepositoryInterface {
	return &UserRepository{
		db: global.GVA_DB,
	}
}

func (r *UserRepository) SearchByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error) {
	var users []*model.User

	query := r.db.WithContext(ctx).Preload("AgentLevel")
	
	// Search by exact match or partial match
	if err := query.Where("invitation_code ILIKE ?", "%"+invitationCode+"%").
		Order("created_at DESC").
		Limit(50). // Limit results to prevent large queries
		Find(&users).Error; err != nil {
		global.GVA_LOG.Error("Failed to search users by invitation code", zap.Error(err))
		return nil, fmt.Errorf("failed to search users by invitation code: %w", err)
	}

	return users, nil
}

func (r *UserRepository) GetReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error) {
	var snapshot model.ReferralSnapshot

	if err := r.db.WithContext(ctx).Where("user_id = ?", userId).First(&snapshot).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("referral snapshot not found for user")
		}
		global.GVA_LOG.Error("Failed to get referral snapshot", zap.Error(err))
		return nil, fmt.Errorf("failed to get referral snapshot: %w", err)
	}

	return &snapshot, nil
}

func (r *UserRepository) GetReferralTree(ctx context.Context, userId uuid.UUID, depth int) (*agent_referral_admin.UserReferralTreeResult, error) {
	// Get the user
	var user model.User
	if err := r.db.WithContext(ctx).Preload("AgentLevel").Where("id = ?", userId).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		global.GVA_LOG.Error("Failed to get user", zap.Error(err))
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Build referral tree recursively
	tree, err := r.buildReferralTree(ctx, userId, 0, depth)
	if err != nil {
		return nil, fmt.Errorf("failed to build referral tree: %w", err)
	}

	// Calculate statistics
	stats := r.calculateTreeStats(tree)

	return &agent_referral_admin.UserReferralTreeResult{
		UserID:                   userId,
		User:                     &user,
		ReferralTree:             tree,
		TotalNodes:               stats.TotalNodes,
		MaxDepth:                 stats.MaxDepth,
		DirectCount:              stats.DirectCount,
		ActiveUsers:              stats.ActiveUsers,
		TradingUsers:             stats.TradingUsers,
		TotalVolumeUSD:           stats.TotalVolumeUSD,
		TotalCommissionEarnedUSD: stats.TotalCommissionEarnedUSD,
	}, nil
}

func (r *UserRepository) UpdateAgentLevel(ctx context.Context, input *agent_referral_admin.UpdateUserAgentLevelInput) (*model.User, error) {
	var user model.User
	if err := r.db.WithContext(ctx).Where("id = ?", input.UserID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		global.GVA_LOG.Error("Failed to get user for agent level update", zap.Error(err))
		return nil, fmt.Errorf("failed to get user for agent level update: %w", err)
	}

	// Update agent level
	user.AgentLevelID = uint(input.AgentLevelID)

	if err := r.db.WithContext(ctx).Save(&user).Error; err != nil {
		global.GVA_LOG.Error("Failed to update user agent level", zap.Error(err))
		return nil, fmt.Errorf("failed to update user agent level: %w", err)
	}

	// Load the agent level relationship
	if err := r.db.WithContext(ctx).Preload("AgentLevel").Where("id = ?", user.ID).First(&user).Error; err != nil {
		global.GVA_LOG.Warn("Failed to load agent level relationship", zap.Error(err))
	}

	return &user, nil
}

func (r *UserRepository) RecalculateReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error) {
	// TODO: Implement logic to recalculate referral snapshot
	// This would involve recalculating all the statistics in the referral snapshot
	// based on current data from referrals, transactions, commissions, etc.

	// For now, just return the existing snapshot
	return r.GetReferralSnapshot(ctx, userId)
}

func (r *UserRepository) CreateReferralRelationship(ctx context.Context, input *agent_referral_admin.CreateReferralInput) (*agent_referral_admin.CreateReferralResult, error) {
	// Check if relationship already exists
	var existingReferral model.Referral
	err := r.db.WithContext(ctx).Where("user_id = ? AND referrer_id = ? AND depth = ?", 
		input.UserID, input.ReferrerID, input.Depth).First(&existingReferral).Error
	
	if err == nil {
		return nil, fmt.Errorf("referral relationship already exists")
	}
	
	if err != gorm.ErrRecordNotFound {
		global.GVA_LOG.Error("Failed to check existing referral relationship", zap.Error(err))
		return nil, fmt.Errorf("failed to check existing referral relationship: %w", err)
	}

	// Create new referral relationship
	referral := &model.Referral{
		UserID:     input.UserID,
		ReferrerID: &input.ReferrerID,
		Depth:      input.Depth,
	}

	if err := r.db.WithContext(ctx).Create(referral).Error; err != nil {
		global.GVA_LOG.Error("Failed to create referral relationship", zap.Error(err))
		return nil, fmt.Errorf("failed to create referral relationship: %w", err)
	}

	return &agent_referral_admin.CreateReferralResult{
		CreatedRelationships: 1,
	}, nil
}

func (r *UserRepository) DeleteReferralRelationship(ctx context.Context, input *agent_referral_admin.DeleteReferralInput) error {
	result := r.db.WithContext(ctx).Where("user_id = ? AND referrer_id = ?", 
		input.UserID, input.ReferrerID).Delete(&model.Referral{})
	
	if result.Error != nil {
		global.GVA_LOG.Error("Failed to delete referral relationship", zap.Error(result.Error))
		return fmt.Errorf("failed to delete referral relationship: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("referral relationship not found")
	}

	return nil
}

// Helper functions

func (r *UserRepository) buildReferralTree(ctx context.Context, userId uuid.UUID, currentDepth, maxDepth int) ([]*agent_referral_admin.UserReferralNode, error) {
	if currentDepth >= maxDepth {
		return []*agent_referral_admin.UserReferralNode{}, nil
	}

	// Get direct referrals
	var referrals []model.Referral
	if err := r.db.WithContext(ctx).
		Preload("User").
		Preload("User.AgentLevel").
		Where("referrer_id = ? AND depth = 1", userId).
		Find(&referrals).Error; err != nil {
		return nil, fmt.Errorf("failed to get referrals: %w", err)
	}

	var nodes []*agent_referral_admin.UserReferralNode
	for _, referral := range referrals {
		// Get referral snapshot for statistics
		var snapshot model.ReferralSnapshot
		r.db.WithContext(ctx).Where("user_id = ?", referral.UserID).First(&snapshot)

		// Build child nodes recursively
		children, err := r.buildReferralTree(ctx, referral.UserID, currentDepth+1, maxDepth)
		if err != nil {
			global.GVA_LOG.Warn("Failed to build child tree", zap.String("userId", referral.UserID.String()), zap.Error(err))
			children = []*agent_referral_admin.UserReferralNode{}
		}

		node := &agent_referral_admin.UserReferralNode{
			UserID:                   referral.UserID,
			User:                     &referral.User,
			ParentUserID:             referral.ReferrerID,
			Depth:                    currentDepth + 1,
			Level:                    currentDepth + 1,
			IsActive:                 referral.User.FirstTransactionAt != nil,
			IsTrading:                referral.User.FirstTransactionAt != nil,
			AgentLevelID:             int(referral.User.AgentLevelID),
			AgentLevel:               &referral.User.AgentLevel,
			DirectReferrals:          snapshot.DirectCount,
			TotalDownline:            snapshot.TotalDownlineCount,
			VolumeUSD:                float64(snapshot.TotalPerpsVolumeUSD.InexactFloat64() + snapshot.TotalMemeVolumeUSD.InexactFloat64()),
			CommissionEarnedUSD:      float64(snapshot.TotalCommissionEarnedUSD.InexactFloat64()),
			Children:                 children,
		}

		nodes = append(nodes, node)
	}

	return nodes, nil
}

type TreeStats struct {
	TotalNodes               int
	MaxDepth                 int
	DirectCount              int
	ActiveUsers              int
	TradingUsers             int
	TotalVolumeUSD           float64
	TotalCommissionEarnedUSD float64
}

func (r *UserRepository) calculateTreeStats(nodes []*agent_referral_admin.UserReferralNode) *TreeStats {
	stats := &TreeStats{
		DirectCount: len(nodes),
	}

	for _, node := range nodes {
		stats.TotalNodes++
		if node.Depth > stats.MaxDepth {
			stats.MaxDepth = node.Depth
		}
		if node.IsActive {
			stats.ActiveUsers++
		}
		if node.IsTrading {
			stats.TradingUsers++
		}
		stats.TotalVolumeUSD += node.VolumeUSD
		stats.TotalCommissionEarnedUSD += node.CommissionEarnedUSD

		// Recursively calculate for children
		childStats := r.calculateTreeStats(node.Children)
		stats.TotalNodes += childStats.TotalNodes
		if childStats.MaxDepth > stats.MaxDepth {
			stats.MaxDepth = childStats.MaxDepth
		}
		stats.ActiveUsers += childStats.ActiveUsers
		stats.TradingUsers += childStats.TradingUsers
		stats.TotalVolumeUSD += childStats.TotalVolumeUSD
		stats.TotalCommissionEarnedUSD += childStats.TotalCommissionEarnedUSD
	}

	return stats
}
