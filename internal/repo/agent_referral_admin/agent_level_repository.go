package agent_referral_admin

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral_admin"
)

// AgentLevelRepository implements AgentLevelRepositoryInterface
type AgentLevelRepository struct {
	db *gorm.DB
}

// NewAgentLevelRepository creates a new AgentLevelRepository
func NewAgentLevelRepository() AgentLevelRepositoryInterface {
	return &AgentLevelRepository{
		db: global.GVA_DB,
	}
}

func (r *AgentLevelRepository) GetAllWithStats(ctx context.Context) ([]*agent_referral_admin.AgentLevelWithStats, error) {
	var levels []model.AgentLevel
	if err := r.db.WithContext(ctx).Order("id ASC").Find(&levels).Error; err != nil {
		global.GVA_LOG.Error("Failed to get agent levels", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent levels: %w", err)
	}

	var result []*agent_referral_admin.AgentLevelWithStats
	for _, level := range levels {
		stats, err := r.calculateLevelStats(ctx, int(level.ID))
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate stats for level", zap.Uint("levelId", level.ID), zap.Error(err))
			stats = &agent_referral_admin.AgentLevelWithStats{
				AgentLevel:             &level,
				UserCount:              0,
				ActiveUserCount:        0,
				TotalVolumeUSD:         0,
				TotalCommissionPaidUSD: 0,
			}
		} else {
			stats.AgentLevel = &level
		}
		result = append(result, stats)
	}

	return result, nil
}

func (r *AgentLevelRepository) GetByIDWithStats(ctx context.Context, id int) (*agent_referral_admin.AgentLevelWithStats, error) {
	var level model.AgentLevel
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&level).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("agent level not found")
		}
		global.GVA_LOG.Error("Failed to get agent level by ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent level by ID: %w", err)
	}

	stats, err := r.calculateLevelStats(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("Failed to calculate stats for level", zap.Int("levelId", id), zap.Error(err))
		return nil, fmt.Errorf("failed to calculate stats for level: %w", err)
	}

	stats.AgentLevel = &level
	return stats, nil
}

func (r *AgentLevelRepository) GetByID(ctx context.Context, id int) (*model.AgentLevel, error) {
	var level model.AgentLevel
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&level).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("agent level not found")
		}
		global.GVA_LOG.Error("Failed to get agent level by ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent level by ID: %w", err)
	}

	return &level, nil
}

func (r *AgentLevelRepository) Update(ctx context.Context, level *model.AgentLevel) error {
	if err := r.db.WithContext(ctx).Save(level).Error; err != nil {
		global.GVA_LOG.Error("Failed to update agent level", zap.Error(err))
		return fmt.Errorf("failed to update agent level: %w", err)
	}

	return nil
}

func (r *AgentLevelRepository) calculateLevelStats(ctx context.Context, levelId int) (*agent_referral_admin.AgentLevelWithStats, error) {
	stats := &agent_referral_admin.AgentLevelWithStats{}

	// Count total users at this level
	var userCount int64
	if err := r.db.WithContext(ctx).Model(&model.User{}).Where("agent_level_id = ?", levelId).Count(&userCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}
	stats.UserCount = int(userCount)

	// Count active users (users with first_transaction_at not null)
	var activeUserCount int64
	if err := r.db.WithContext(ctx).Model(&model.User{}).
		Where("agent_level_id = ? AND first_transaction_at IS NOT NULL", levelId).
		Count(&activeUserCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count active users: %w", err)
	}
	stats.ActiveUserCount = int(activeUserCount)

	// Calculate total volume USD from referral snapshots
	var totalVolumeUSD float64
	if err := r.db.WithContext(ctx).Model(&model.ReferralSnapshot{}).
		Joins("JOIN users ON referral_snapshots.user_id = users.id").
		Where("users.agent_level_id = ?", levelId).
		Select("COALESCE(SUM(referral_snapshots.total_perps_volume_usd + referral_snapshots.total_meme_volume_usd), 0)").
		Scan(&totalVolumeUSD).Error; err != nil {
		global.GVA_LOG.Warn("Failed to calculate total volume", zap.Error(err))
		totalVolumeUSD = 0
	}
	stats.TotalVolumeUSD = totalVolumeUSD

	// Calculate total commission paid USD from referral snapshots
	var totalCommissionPaidUSD float64
	if err := r.db.WithContext(ctx).Model(&model.ReferralSnapshot{}).
		Joins("JOIN users ON referral_snapshots.user_id = users.id").
		Where("users.agent_level_id = ?", levelId).
		Select("COALESCE(SUM(referral_snapshots.total_commission_earned_usd), 0)").
		Scan(&totalCommissionPaidUSD).Error; err != nil {
		global.GVA_LOG.Warn("Failed to calculate total commission paid", zap.Error(err))
		totalCommissionPaidUSD = 0
	}
	stats.TotalCommissionPaidUSD = totalCommissionPaidUSD

	return stats, nil
}
