// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"
)

type ActivityTask struct {
	ID                 string            `json:"id"`
	CategoryID         string            `json:"categoryId"`
	Category           *TaskCategory     `json:"category"`
	Name               *MultilingualName `json:"name"`
	Description        *string           `json:"description,omitempty"`
	Frequency          TaskFrequency     `json:"frequency"`
	TaskIdentifier     *TaskIdentifier   `json:"taskIdentifier,omitempty"`
	Points             int               `json:"points"`
	MaxCompletions     *int              `json:"maxCompletions,omitempty"`
	ResetPeriod        *string           `json:"resetPeriod,omitempty"`
	Conditions         *string           `json:"conditions,omitempty"`
	ActionTarget       *string           `json:"actionTarget,omitempty"`
	VerificationMethod *string           `json:"verificationMethod,omitempty"`
	ExternalLink       *string           `json:"externalLink,omitempty"`
	TaskIcon           *string           `json:"taskIcon,omitempty"`
	ButtonText         *string           `json:"buttonText,omitempty"`
	StartDate          *int64            `json:"startDate,omitempty"`
	EndDate            *int64            `json:"endDate,omitempty"`
	SortOrder          int               `json:"sortOrder"`
	IsActive           bool              `json:"isActive"`
	CreatedAt          time.Time         `json:"createdAt"`
	UpdatedAt          time.Time         `json:"updatedAt"`
}

type AdminAgentLevel struct {
	ID                      int     `json:"id"`
	Name                    string  `json:"name"`
	MemeVolumeThreshold     float64 `json:"memeVolumeThreshold"`
	ContractVolumeThreshold float64 `json:"contractVolumeThreshold"`
	MemeFeeRate             float64 `json:"memeFeeRate"`
	TakerFeeRate            float64 `json:"takerFeeRate"`
	MakerFeeRate            float64 `json:"makerFeeRate"`
	DirectCommissionRate    float64 `json:"directCommissionRate"`
	IndirectCommissionRate  float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate  float64 `json:"extendedCommissionRate"`
	MemeFeeRebate           float64 `json:"memeFeeRebate"`
	UserCount               int     `json:"userCount"`
	ActiveUserCount         int     `json:"activeUserCount"`
	TotalVolumeUsd          float64 `json:"totalVolumeUSD"`
	TotalCommissionPaidUsd  float64 `json:"totalCommissionPaidUSD"`
}

type AdminAgentLevelResponse struct {
	AgentLevel *AdminAgentLevel `json:"agentLevel,omitempty"`
	Success    bool             `json:"success"`
	Message    string           `json:"message"`
}

type AdminAgentReferralStatsResponse struct {
	TotalUsers             int                       `json:"totalUsers"`
	TotalActiveUsers       int                       `json:"totalActiveUsers"`
	TotalTradingUsers      int                       `json:"totalTradingUsers"`
	TotalInfiniteAgents    int                       `json:"totalInfiniteAgents"`
	TotalReferralTrees     int                       `json:"totalReferralTrees"`
	TotalVolumeUsd         float64                   `json:"totalVolumeUSD"`
	TotalCommissionPaidUsd float64                   `json:"totalCommissionPaidUSD"`
	TotalCashbackPaidUsd   float64                   `json:"totalCashbackPaidUSD"`
	AverageTreeDepth       float64                   `json:"averageTreeDepth"`
	AverageDirectReferrals float64                   `json:"averageDirectReferrals"`
	LevelDistribution      []*AdminLevelDistribution `json:"levelDistribution"`
	DailyStats             []*AdminDailyAgentStats   `json:"dailyStats"`
	Success                bool                      `json:"success"`
	Message                string                    `json:"message"`
}

type AdminCommissionDistributionResponse struct {
	TotalCommissionPaidUsd     float64                          `json:"totalCommissionPaidUSD"`
	DirectCommissionUsd        float64                          `json:"directCommissionUSD"`
	IndirectCommissionUsd      float64                          `json:"indirectCommissionUSD"`
	ExtendedCommissionUsd      float64                          `json:"extendedCommissionUSD"`
	InfiniteAgentCommissionUsd float64                          `json:"infiniteAgentCommissionUSD"`
	LevelBreakdown             []*AdminCommissionLevelBreakdown `json:"levelBreakdown"`
	DailyDistribution          []*AdminDailyCommissionStats     `json:"dailyDistribution"`
	Success                    bool                             `json:"success"`
	Message                    string                           `json:"message"`
}

type AdminCommissionLevelBreakdown struct {
	LevelID               int     `json:"levelId"`
	LevelName             string  `json:"levelName"`
	TotalCommissionUsd    float64 `json:"totalCommissionUSD"`
	DirectCommissionUsd   float64 `json:"directCommissionUSD"`
	IndirectCommissionUsd float64 `json:"indirectCommissionUSD"`
	ExtendedCommissionUsd float64 `json:"extendedCommissionUSD"`
	UserCount             int     `json:"userCount"`
}

type AdminCreateAllTreeSnapshotsResponse struct {
	Success        bool   `json:"success"`
	Message        string `json:"message"`
	ProcessedTrees int    `json:"processedTrees"`
	ErrorCount     int    `json:"errorCount"`
	TotalNodes     int    `json:"totalNodes"`
}

type AdminCreateInfiniteAgentInput struct {
	UserID          string               `json:"userId"`
	CommissionRateN float64              `json:"commissionRateN"`
	Status          *InfiniteAgentStatus `json:"status,omitempty"`
	Description     *string              `json:"description,omitempty"`
}

type AdminCreateInfiniteAgentTreesInput struct {
	InfiniteAgentUserIds []string   `json:"infiniteAgentUserIds,omitempty"`
	SnapshotDate         *time.Time `json:"snapshotDate,omitempty"`
	ForceRecalculate     *bool      `json:"forceRecalculate,omitempty"`
}

type AdminCreateInfiniteAgentTreesResponse struct {
	Success             bool   `json:"success"`
	Message             string `json:"message"`
	ProcessedCount      int    `json:"processedCount"`
	ErrorCount          int    `json:"errorCount"`
	TotalInfiniteAgents int    `json:"totalInfiniteAgents"`
	SnapshotDate        string `json:"snapshotDate"`
}

type AdminCreateReferralInput struct {
	UserID     string `json:"userId"`
	ReferrerID string `json:"referrerId"`
	Depth      *int   `json:"depth,omitempty"`
}

type AdminCreateReferralResponse struct {
	Success              bool   `json:"success"`
	Message              string `json:"message"`
	CreatedRelationships int    `json:"createdRelationships"`
}

type AdminCreateTreeSnapshotInput struct {
	RootUserID   string     `json:"rootUserId"`
	SnapshotDate *time.Time `json:"snapshotDate,omitempty"`
	Description  *string    `json:"description,omitempty"`
}

type AdminCreateTreeSnapshotResponse struct {
	Success        bool    `json:"success"`
	Message        string  `json:"message"`
	TreeID         *string `json:"treeId,omitempty"`
	ProcessedNodes int     `json:"processedNodes"`
}

type AdminDailyAgentStats struct {
	Date              string  `json:"date"`
	NewUsers          int     `json:"newUsers"`
	ActiveUsers       int     `json:"activeUsers"`
	TradingUsers      int     `json:"tradingUsers"`
	VolumeUsd         float64 `json:"volumeUSD"`
	CommissionPaidUsd float64 `json:"commissionPaidUSD"`
	CashbackPaidUsd   float64 `json:"cashbackPaidUSD"`
}

type AdminDailyCommissionStats struct {
	Date                       string  `json:"date"`
	TotalCommissionUsd         float64 `json:"totalCommissionUSD"`
	DirectCommissionUsd        float64 `json:"directCommissionUSD"`
	IndirectCommissionUsd      float64 `json:"indirectCommissionUSD"`
	ExtendedCommissionUsd      float64 `json:"extendedCommissionUSD"`
	InfiniteAgentCommissionUsd float64 `json:"infiniteAgentCommissionUSD"`
}

type AdminDailyInfiniteAgentStats struct {
	Date                string  `json:"date"`
	CommissionEarnedUsd float64 `json:"commissionEarnedUSD"`
	NetFeeUsd           float64 `json:"netFeeUSD"`
	VolumeUsd           float64 `json:"volumeUSD"`
	ActiveUsers         int     `json:"activeUsers"`
	TradingUsers        int     `json:"tradingUsers"`
	NewNodes            int     `json:"newNodes"`
}

type AdminDeleteReferralInput struct {
	UserID     string `json:"userId"`
	ReferrerID string `json:"referrerId"`
}

type AdminDeleteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type AdminInfiniteAgent struct {
	ID                             string     `json:"id"`
	UserID                         string     `json:"userId"`
	User                           *AdminUser `json:"user"`
	CommissionRateN                float64    `json:"commissionRateN"`
	TotalNetFeeUsd                 float64    `json:"totalNetFeeUSD"`
	TotalStandardCommissionPaidUsd float64    `json:"totalStandardCommissionPaidUSD"`
	FinalCommissionAmountUsd       float64    `json:"finalCommissionAmountUSD"`
	MemeTotalFeeUsd                float64    `json:"memeTotalFeeUSD"`
	MemePaidCommissionUsd          float64    `json:"memePaidCommissionUSD"`
	MemeNetFeeUsd                  float64    `json:"memeNetFeeUSD"`
	MemeActivityCashbackUsd        float64    `json:"memeActivityCashbackUSD"`
	ContractTotalFeeUsd            float64    `json:"contractTotalFeeUSD"`
	ContractPaidCommissionUsd      float64    `json:"contractPaidCommissionUSD"`
	ContractNetFeeUsd              float64    `json:"contractNetFeeUSD"`
	Status                         string     `json:"status"`
	Description                    *string    `json:"description,omitempty"`
	CreatedAt                      time.Time  `json:"createdAt"`
	UpdatedAt                      time.Time  `json:"updatedAt"`
	LastCalculatedAt               *time.Time `json:"lastCalculatedAt,omitempty"`
}

type AdminInfiniteAgentContractBreakdown struct {
	TotalFeeUsd       float64 `json:"totalFeeUSD"`
	PaidCommissionUsd float64 `json:"paidCommissionUSD"`
	NetFeeUsd         float64 `json:"netFeeUSD"`
	TransactionCount  int     `json:"transactionCount"`
	VolumeUsd         float64 `json:"volumeUSD"`
}

type AdminInfiniteAgentMemeBreakdown struct {
	TotalFeeUsd         float64 `json:"totalFeeUSD"`
	PaidCommissionUsd   float64 `json:"paidCommissionUSD"`
	NetFeeUsd           float64 `json:"netFeeUSD"`
	ActivityCashbackUsd float64 `json:"activityCashbackUSD"`
	TransactionCount    int     `json:"transactionCount"`
	VolumeUsd           float64 `json:"volumeUSD"`
}

type AdminInfiniteAgentPerformanceResponse struct {
	InfiniteAgentUserID            *string                              `json:"infiniteAgentUserId,omitempty"`
	InfiniteAgentUser              *AdminUser                           `json:"infiniteAgentUser,omitempty"`
	TotalCommissionEarnedUsd       float64                              `json:"totalCommissionEarnedUSD"`
	TotalNetFeeUsd                 float64                              `json:"totalNetFeeUSD"`
	TotalStandardCommissionPaidUsd float64                              `json:"totalStandardCommissionPaidUSD"`
	FinalCommissionAmountUsd       float64                              `json:"finalCommissionAmountUSD"`
	CommissionRateN                float64                              `json:"commissionRateN"`
	TreeCount                      int                                  `json:"treeCount"`
	TotalNodes                     int                                  `json:"totalNodes"`
	ActiveUsers                    int                                  `json:"activeUsers"`
	TradingUsers                   int                                  `json:"tradingUsers"`
	TotalVolumeUsd                 float64                              `json:"totalVolumeUSD"`
	MemeBreakdown                  *AdminInfiniteAgentMemeBreakdown     `json:"memeBreakdown"`
	ContractBreakdown              *AdminInfiniteAgentContractBreakdown `json:"contractBreakdown"`
	DailyPerformance               []*AdminDailyInfiniteAgentStats      `json:"dailyPerformance"`
	TreeBreakdown                  []*AdminInfiniteAgentTreeBreakdown   `json:"treeBreakdown"`
	Success                        bool                                 `json:"success"`
	Message                        string                               `json:"message"`
}

type AdminInfiniteAgentResponse struct {
	InfiniteAgent *AdminInfiniteAgent `json:"infiniteAgent,omitempty"`
	Success       bool                `json:"success"`
	Message       string              `json:"message"`
}

type AdminInfiniteAgentStatsInput struct {
	InfiniteAgentUserID  *string   `json:"infiniteAgentUserId,omitempty"`
	StartDate            time.Time `json:"startDate"`
	EndDate              time.Time `json:"endDate"`
	IncludeTreeBreakdown *bool     `json:"includeTreeBreakdown,omitempty"`
}

type AdminInfiniteAgentTree struct {
	ID                    string                        `json:"id"`
	InfiniteAgentUserID   string                        `json:"infiniteAgentUserId"`
	InfiniteAgentUser     *AdminUser                    `json:"infiniteAgentUser"`
	CommissionRateN       float64                       `json:"commissionRateN"`
	RootUserID            string                        `json:"rootUserId"`
	RootUser              *AdminUser                    `json:"rootUser"`
	SnapshotDate          time.Time                     `json:"snapshotDate"`
	TotalNodes            int                           `json:"totalNodes"`
	MaxDepth              int                           `json:"maxDepth"`
	DirectCount           int                           `json:"directCount"`
	ActiveUsers           int                           `json:"activeUsers"`
	TradingUsers          int                           `json:"tradingUsers"`
	TotalCommissionEarned float64                       `json:"totalCommissionEarned"`
	TotalVolumeUsd        float64                       `json:"totalVolumeUsd"`
	Status                string                        `json:"status"`
	Description           *string                       `json:"description,omitempty"`
	CreatedAt             time.Time                     `json:"createdAt"`
	TreeNodes             []*AdminInfiniteAgentTreeNode `json:"treeNodes"`
}

type AdminInfiniteAgentTreeBreakdown struct {
	TreeID              string     `json:"treeId"`
	RootUserID          string     `json:"rootUserId"`
	RootUser            *AdminUser `json:"rootUser"`
	TotalNodes          int        `json:"totalNodes"`
	ActiveUsers         int        `json:"activeUsers"`
	TradingUsers        int        `json:"tradingUsers"`
	VolumeUsd           float64    `json:"volumeUSD"`
	CommissionEarnedUsd float64    `json:"commissionEarnedUSD"`
	NetFeeUsd           float64    `json:"netFeeUSD"`
	SnapshotDate        time.Time  `json:"snapshotDate"`
}

type AdminInfiniteAgentTreeNode struct {
	ID               string           `json:"id"`
	TreeID           string           `json:"treeId"`
	UserID           string           `json:"userId"`
	User             *AdminUser       `json:"user"`
	ParentUserID     *string          `json:"parentUserId,omitempty"`
	ParentUser       *AdminUser       `json:"parentUser,omitempty"`
	ReferrerID       *string          `json:"referrerId,omitempty"`
	Referrer         *AdminUser       `json:"referrer,omitempty"`
	Depth            int              `json:"depth"`
	Level            int              `json:"level"`
	Position         int              `json:"position"`
	IsActive         bool             `json:"isActive"`
	IsTrading        bool             `json:"isTrading"`
	AgentLevelID     int              `json:"agentLevelId"`
	AgentLevel       *AdminAgentLevel `json:"agentLevel"`
	CommissionEarned float64          `json:"commissionEarned"`
	VolumeUsd        float64          `json:"volumeUsd"`
	FeeVolumeUsd     float64          `json:"feeVolumeUsd"`
	CreatedAt        time.Time        `json:"createdAt"`
}

type AdminInfiniteAgentTreesInput struct {
	Page                *int                 `json:"page,omitempty"`
	PageSize            *int                 `json:"pageSize,omitempty"`
	InfiniteAgentUserID *string              `json:"infiniteAgentUserId,omitempty"`
	StartDate           *time.Time           `json:"startDate,omitempty"`
	EndDate             *time.Time           `json:"endDate,omitempty"`
	SortBy              *AgentReferralSortBy `json:"sortBy,omitempty"`
	SortOrder           *SortOrder           `json:"sortOrder,omitempty"`
}

type AdminInfiniteAgentTreesResponse struct {
	Trees       []*AdminInfiniteAgentTree `json:"trees"`
	TotalCount  int                       `json:"totalCount"`
	CurrentPage int                       `json:"currentPage"`
	TotalPages  int                       `json:"totalPages"`
	Success     bool                      `json:"success"`
	Message     string                    `json:"message"`
}

type AdminInfiniteAgentsResponse struct {
	InfiniteAgents []*AdminInfiniteAgent `json:"infiniteAgents"`
	TotalCount     int                   `json:"totalCount"`
	CurrentPage    int                   `json:"currentPage"`
	TotalPages     int                   `json:"totalPages"`
	Success        bool                  `json:"success"`
	Message        string                `json:"message"`
}

type AdminLevelDistribution struct {
	LevelID                int     `json:"levelId"`
	LevelName              string  `json:"levelName"`
	UserCount              int     `json:"userCount"`
	Percentage             float64 `json:"percentage"`
	TotalVolumeUsd         float64 `json:"totalVolumeUSD"`
	TotalCommissionPaidUsd float64 `json:"totalCommissionPaidUSD"`
}

type AdminPaginationInput struct {
	Page      *int       `json:"page,omitempty"`
	PageSize  *int       `json:"pageSize,omitempty"`
	SortBy    *string    `json:"sortBy,omitempty"`
	SortOrder *SortOrder `json:"sortOrder,omitempty"`
}

type AdminReferralSnapshot struct {
	UserID                   string    `json:"userId"`
	DirectCount              int       `json:"directCount"`
	TotalDownlineCount       int       `json:"totalDownlineCount"`
	TradingUserCount         int       `json:"tradingUserCount"`
	TotalVolumeUsd           float64   `json:"totalVolumeUSD"`
	TotalRewardsDistributed  float64   `json:"totalRewardsDistributed"`
	TotalPerpsVolumeUsd      float64   `json:"totalPerpsVolumeUSD"`
	TotalPerpsFees           float64   `json:"totalPerpsFees"`
	TotalPerpsFeesPaid       float64   `json:"totalPerpsFeesPaid"`
	TotalPerpsFeesUnPaid     float64   `json:"totalPerpsFeesUnPaid"`
	TotalPerpsTradesCount    int       `json:"totalPerpsTradesCount"`
	TotalMemeVolumeUsd       float64   `json:"totalMemeVolumeUSD"`
	TotalMemeFees            float64   `json:"totalMemeFees"`
	TotalMemeFeesPaid        float64   `json:"totalMemeFeesPaid"`
	TotalMemeFeesUnPaid      float64   `json:"totalMemeFeesUnPaid"`
	TotalMemeTradesCount     int       `json:"totalMemeTradesCount"`
	TotalCommissionEarnedUsd float64   `json:"totalCommissionEarnedUSD"`
	ClaimedCommissionUsd     float64   `json:"claimedCommissionUSD"`
	UnclaimedCommissionUsd   float64   `json:"unclaimedCommissionUSD"`
	TotalCashbackEarnedUsd   float64   `json:"totalCashbackEarnedUSD"`
	ClaimedCashbackUsd       float64   `json:"claimedCashbackUSD"`
	UnclaimedCashbackUsd     float64   `json:"unclaimedCashbackUSD"`
	LastUpdatedAt            time.Time `json:"lastUpdatedAt"`
}

type AdminReferralTree struct {
	ID                     string                   `json:"id"`
	RootUserID             string                   `json:"rootUserId"`
	RootUser               *AdminUser               `json:"rootUser"`
	SnapshotDate           time.Time                `json:"snapshotDate"`
	TotalNodes             int                      `json:"totalNodes"`
	MaxDepth               int                      `json:"maxDepth"`
	DirectCount            int                      `json:"directCount"`
	ActiveUsers            int                      `json:"activeUsers"`
	TradingUsers           int                      `json:"tradingUsers"`
	InfiniteAgentUserID    *string                  `json:"infiniteAgentUserId,omitempty"`
	InfiniteAgentUser      *AdminUser               `json:"infiniteAgentUser,omitempty"`
	HasInfiniteAgent       bool                     `json:"hasInfiniteAgent"`
	Description            *string                  `json:"description,omitempty"`
	IsValid                bool                     `json:"isValid"`
	CreatedAt              time.Time                `json:"createdAt"`
	TotalVolumeUsd         float64                  `json:"totalVolumeUSD"`
	TotalCommissionPaidUsd float64                  `json:"totalCommissionPaidUSD"`
	Nodes                  []*AdminReferralTreeNode `json:"nodes"`
}

type AdminReferralTreeNode struct {
	ID                  string           `json:"id"`
	TreeSnapshotID      string           `json:"treeSnapshotId"`
	UserID              string           `json:"userId"`
	User                *AdminUser       `json:"user"`
	ParentUserID        *string          `json:"parentUserId,omitempty"`
	ParentUser          *AdminUser       `json:"parentUser,omitempty"`
	ReferrerID          *string          `json:"referrerId,omitempty"`
	Referrer            *AdminUser       `json:"referrer,omitempty"`
	Depth               int              `json:"depth"`
	Level               int              `json:"level"`
	Position            int              `json:"position"`
	IsActive            bool             `json:"isActive"`
	IsTrading           bool             `json:"isTrading"`
	AgentLevelID        int              `json:"agentLevelId"`
	AgentLevel          *AdminAgentLevel `json:"agentLevel"`
	VolumeUsd           float64          `json:"volumeUSD"`
	CommissionEarnedUsd float64          `json:"commissionEarnedUSD"`
	CreatedAt           time.Time        `json:"createdAt"`
}

type AdminReferralTreeResponse struct {
	Tree    *AdminReferralTree `json:"tree,omitempty"`
	Success bool               `json:"success"`
	Message string             `json:"message"`
}

type AdminReferralTreesInput struct {
	Page             *int                 `json:"page,omitempty"`
	PageSize         *int                 `json:"pageSize,omitempty"`
	RootUserID       *string              `json:"rootUserId,omitempty"`
	HasInfiniteAgent *bool                `json:"hasInfiniteAgent,omitempty"`
	StartDate        *time.Time           `json:"startDate,omitempty"`
	EndDate          *time.Time           `json:"endDate,omitempty"`
	SortBy           *AgentReferralSortBy `json:"sortBy,omitempty"`
	SortOrder        *SortOrder           `json:"sortOrder,omitempty"`
}

type AdminReferralTreesResponse struct {
	Trees       []*AdminReferralTree `json:"trees"`
	TotalCount  int                  `json:"totalCount"`
	CurrentPage int                  `json:"currentPage"`
	TotalPages  int                  `json:"totalPages"`
	Success     bool                 `json:"success"`
	Message     string               `json:"message"`
}

type AdminStatsInput struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
}

type AdminSystemOperationResponse struct {
	Success        bool   `json:"success"`
	Message        string `json:"message"`
	ProcessedCount int    `json:"processedCount"`
	ErrorCount     int    `json:"errorCount"`
	Duration       string `json:"duration"`
}

type AdminTaskCompletionStats struct {
	TaskCompletions []*TaskCompletionStat `json:"taskCompletions"`
	StartDate       time.Time             `json:"startDate"`
	EndDate         time.Time             `json:"endDate"`
	TotalTasks      int                   `json:"totalTasks"`
}

type AdminTaskCompletionStatsResponse struct {
	Success bool                      `json:"success"`
	Message string                    `json:"message"`
	Data    *AdminTaskCompletionStats `json:"data,omitempty"`
}

type AdminTierDistributionResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    []*TierDistributionStat `json:"data"`
}

type AdminTopAgent struct {
	UserID                   string           `json:"userId"`
	User                     *AdminUser       `json:"user"`
	AgentLevelID             int              `json:"agentLevelId"`
	AgentLevel               *AdminAgentLevel `json:"agentLevel"`
	TotalVolumeUsd           float64          `json:"totalVolumeUSD"`
	TotalCommissionEarnedUsd float64          `json:"totalCommissionEarnedUSD"`
	DirectReferrals          int              `json:"directReferrals"`
	TotalDownline            int              `json:"totalDownline"`
	TradingUsers             int              `json:"tradingUsers"`
	Rank                     int              `json:"rank"`
	IsInfiniteAgent          bool             `json:"isInfiniteAgent"`
}

type AdminTopAgentsInput struct {
	Limit        *int                 `json:"limit,omitempty"`
	SortBy       *AgentReferralSortBy `json:"sortBy,omitempty"`
	StartDate    *time.Time           `json:"startDate,omitempty"`
	EndDate      *time.Time           `json:"endDate,omitempty"`
	AgentLevelID *int                 `json:"agentLevelId,omitempty"`
}

type AdminTopAgentsResponse struct {
	Agents     []*AdminTopAgent `json:"agents"`
	TotalCount int              `json:"totalCount"`
	Success    bool             `json:"success"`
	Message    string           `json:"message"`
}

type AdminUpdateAgentLevelInput struct {
	ID                      int      `json:"id"`
	Name                    *string  `json:"name,omitempty"`
	MemeVolumeThreshold     *float64 `json:"memeVolumeThreshold,omitempty"`
	ContractVolumeThreshold *float64 `json:"contractVolumeThreshold,omitempty"`
	MemeFeeRate             *float64 `json:"memeFeeRate,omitempty"`
	TakerFeeRate            *float64 `json:"takerFeeRate,omitempty"`
	MakerFeeRate            *float64 `json:"makerFeeRate,omitempty"`
	DirectCommissionRate    *float64 `json:"directCommissionRate,omitempty"`
	IndirectCommissionRate  *float64 `json:"indirectCommissionRate,omitempty"`
	ExtendedCommissionRate  *float64 `json:"extendedCommissionRate,omitempty"`
	MemeFeeRebate           *float64 `json:"memeFeeRebate,omitempty"`
}

type AdminUpdateCommissionRatesInput struct {
	LevelID                int     `json:"levelId"`
	DirectCommissionRate   float64 `json:"directCommissionRate"`
	IndirectCommissionRate float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate float64 `json:"extendedCommissionRate"`
}

type AdminUpdateFeeRatesInput struct {
	LevelID       int     `json:"levelId"`
	MemeFeeRate   float64 `json:"memeFeeRate"`
	TakerFeeRate  float64 `json:"takerFeeRate"`
	MakerFeeRate  float64 `json:"makerFeeRate"`
	MemeFeeRebate float64 `json:"memeFeeRebate"`
}

type AdminUpdateInfiniteAgentInput struct {
	ID              string               `json:"id"`
	CommissionRateN *float64             `json:"commissionRateN,omitempty"`
	Status          *InfiniteAgentStatus `json:"status,omitempty"`
	Description     *string              `json:"description,omitempty"`
}

type AdminUpdateUserAgentLevelInput struct {
	UserID       string  `json:"userId"`
	AgentLevelID int     `json:"agentLevelId"`
	Reason       *string `json:"reason,omitempty"`
}

type AdminUpdateVolumeThresholdsInput struct {
	LevelID                 int     `json:"levelId"`
	MemeVolumeThreshold     float64 `json:"memeVolumeThreshold"`
	ContractVolumeThreshold float64 `json:"contractVolumeThreshold"`
}

type AdminUser struct {
	ID                        string                 `json:"id"`
	Email                     *string                `json:"email,omitempty"`
	InvitationCode            *string                `json:"invitationCode,omitempty"`
	AgentLevelID              int                    `json:"agentLevelId"`
	AgentLevel                *AdminAgentLevel       `json:"agentLevel"`
	CreatedAt                 time.Time              `json:"createdAt"`
	UpdatedAt                 time.Time              `json:"updatedAt"`
	FirstTransactionAt        *time.Time             `json:"firstTransactionAt,omitempty"`
	LevelUpgradedAt           *time.Time             `json:"levelUpgradedAt,omitempty"`
	LevelGracePeriodStartedAt *time.Time             `json:"levelGracePeriodStartedAt,omitempty"`
	IsInfiniteAgent           bool                   `json:"isInfiniteAgent"`
	ReferralSnapshot          *AdminReferralSnapshot `json:"referralSnapshot,omitempty"`
}

type AdminUserActivityStats struct {
	DailyCompletions []*DailyCompletionStat `json:"dailyCompletions"`
	StartDate        time.Time              `json:"startDate"`
	EndDate          time.Time              `json:"endDate"`
}

type AdminUserActivityStatsResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    *AdminUserActivityStats `json:"data,omitempty"`
}

type AdminUserReferralNode struct {
	UserID              string                   `json:"userId"`
	User                *AdminUser               `json:"user"`
	ParentUserID        *string                  `json:"parentUserId,omitempty"`
	ParentUser          *AdminUser               `json:"parentUser,omitempty"`
	Depth               int                      `json:"depth"`
	Level               int                      `json:"level"`
	IsActive            bool                     `json:"isActive"`
	IsTrading           bool                     `json:"isTrading"`
	AgentLevelID        int                      `json:"agentLevelId"`
	AgentLevel          *AdminAgentLevel         `json:"agentLevel"`
	DirectReferrals     int                      `json:"directReferrals"`
	TotalDownline       int                      `json:"totalDownline"`
	VolumeUsd           float64                  `json:"volumeUSD"`
	CommissionEarnedUsd float64                  `json:"commissionEarnedUSD"`
	Children            []*AdminUserReferralNode `json:"children"`
}

type AdminUserReferralSnapshotResponse struct {
	Snapshot *AdminReferralSnapshot `json:"snapshot,omitempty"`
	Success  bool                   `json:"success"`
	Message  string                 `json:"message"`
}

type AdminUserReferralTreeResponse struct {
	UserID                   string                   `json:"userId"`
	User                     *AdminUser               `json:"user"`
	ReferralTree             []*AdminUserReferralNode `json:"referralTree"`
	TotalNodes               int                      `json:"totalNodes"`
	MaxDepth                 int                      `json:"maxDepth"`
	DirectCount              int                      `json:"directCount"`
	ActiveUsers              int                      `json:"activeUsers"`
	TradingUsers             int                      `json:"tradingUsers"`
	TotalVolumeUsd           float64                  `json:"totalVolumeUSD"`
	TotalCommissionEarnedUsd float64                  `json:"totalCommissionEarnedUSD"`
	Success                  bool                     `json:"success"`
	Message                  string                   `json:"message"`
}

type AdminUserResponse struct {
	User    *AdminUser `json:"user,omitempty"`
	Success bool       `json:"success"`
	Message string     `json:"message"`
}

type CreateTaskCategoryInput struct {
	Name        TaskCategoryName `json:"name"`
	DisplayName string           `json:"displayName"`
	Description *string          `json:"description,omitempty"`
	Icon        *string          `json:"icon,omitempty"`
	SortOrder   *int             `json:"sortOrder,omitempty"`
}

type CreateTaskInput struct {
	CategoryID         string                 `json:"categoryId"`
	Name               *MultilingualNameInput `json:"name"`
	Description        *string                `json:"description,omitempty"`
	Frequency          TaskFrequency          `json:"frequency"`
	TaskIdentifier     *TaskIdentifier        `json:"taskIdentifier,omitempty"`
	Points             int                    `json:"points"`
	MaxCompletions     *int                   `json:"maxCompletions,omitempty"`
	ResetPeriod        *string                `json:"resetPeriod,omitempty"`
	Conditions         *string                `json:"conditions,omitempty"`
	ActionTarget       *string                `json:"actionTarget,omitempty"`
	VerificationMethod *string                `json:"verificationMethod,omitempty"`
	ExternalLink       *string                `json:"externalLink,omitempty"`
	TaskIcon           *string                `json:"taskIcon,omitempty"`
	ButtonText         *string                `json:"buttonText,omitempty"`
	StartDate          *int64                 `json:"startDate,omitempty"`
	EndDate            *int64                 `json:"endDate,omitempty"`
	SortOrder          *int                   `json:"sortOrder,omitempty"`
}

type CreateTierBenefitInput struct {
	TierLevel           int     `json:"tierLevel"`
	TierName            string  `json:"tierName"`
	MinPoints           int     `json:"minPoints"`
	CashbackPercentage  float64 `json:"cashbackPercentage"`
	NetFee              float64 `json:"netFee"`
	BenefitsDescription *string `json:"benefitsDescription,omitempty"`
	TierColor           *string `json:"tierColor,omitempty"`
	TierIcon            *string `json:"tierIcon,omitempty"`
}

type DailyCompletionStat struct {
	Date            string `json:"date"`
	CompletionCount int    `json:"completionCount"`
}

type MultilingualName struct {
	En string  `json:"en"`
	Zh *string `json:"zh,omitempty"`
	Ja *string `json:"ja,omitempty"`
	Hi *string `json:"hi,omitempty"`
	Hk *string `json:"hk,omitempty"`
	Vi *string `json:"vi,omitempty"`
}

type MultilingualNameInput struct {
	En string  `json:"en"`
	Zh *string `json:"zh,omitempty"`
	Ja *string `json:"ja,omitempty"`
	Hi *string `json:"hi,omitempty"`
	Hk *string `json:"hk,omitempty"`
	Vi *string `json:"vi,omitempty"`
}

type Mutation struct {
}

type Query struct {
}

type TaskCategory struct {
	ID          string           `json:"id"`
	Name        TaskCategoryName `json:"name"`
	DisplayName string           `json:"displayName"`
	Description *string          `json:"description,omitempty"`
	Icon        *string          `json:"icon,omitempty"`
	IsActive    bool             `json:"isActive"`
	SortOrder   int              `json:"sortOrder"`
	CreatedAt   time.Time        `json:"createdAt"`
	UpdatedAt   time.Time        `json:"updatedAt"`
}

type TaskCompletionStat struct {
	TaskName        string `json:"taskName"`
	CompletionCount int    `json:"completionCount"`
}

type TierBenefit struct {
	ID                  string    `json:"id"`
	TierLevel           int       `json:"tierLevel"`
	TierName            string    `json:"tierName"`
	MinPoints           int       `json:"minPoints"`
	CashbackPercentage  float64   `json:"cashbackPercentage"`
	NetFee              float64   `json:"netFee"`
	BenefitsDescription *string   `json:"benefitsDescription,omitempty"`
	TierColor           *string   `json:"tierColor,omitempty"`
	TierIcon            *string   `json:"tierIcon,omitempty"`
	IsActive            bool      `json:"isActive"`
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`
}

type TierBenefitResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Data    *TierBenefit `json:"data,omitempty"`
}

type TierDistributionStat struct {
	TierLevel int `json:"tierLevel"`
	UserCount int `json:"userCount"`
}

type UpdateTaskCategoryInput struct {
	ID          string            `json:"id"`
	Name        *TaskCategoryName `json:"name,omitempty"`
	DisplayName *string           `json:"displayName,omitempty"`
	Description *string           `json:"description,omitempty"`
	Icon        *string           `json:"icon,omitempty"`
	IsActive    *bool             `json:"isActive,omitempty"`
	SortOrder   *int              `json:"sortOrder,omitempty"`
}

type UpdateTaskInput struct {
	ID                 string                 `json:"id"`
	CategoryID         *string                `json:"categoryId,omitempty"`
	Name               *MultilingualNameInput `json:"name,omitempty"`
	Description        *string                `json:"description,omitempty"`
	Frequency          *TaskFrequency         `json:"frequency,omitempty"`
	TaskIdentifier     *TaskIdentifier        `json:"taskIdentifier,omitempty"`
	Points             *int                   `json:"points,omitempty"`
	MaxCompletions     *int                   `json:"maxCompletions,omitempty"`
	ResetPeriod        *string                `json:"resetPeriod,omitempty"`
	Conditions         *string                `json:"conditions,omitempty"`
	ActionTarget       *string                `json:"actionTarget,omitempty"`
	VerificationMethod *string                `json:"verificationMethod,omitempty"`
	ExternalLink       *string                `json:"externalLink,omitempty"`
	TaskIcon           *string                `json:"taskIcon,omitempty"`
	ButtonText         *string                `json:"buttonText,omitempty"`
	StartDate          *int64                 `json:"startDate,omitempty"`
	EndDate            *int64                 `json:"endDate,omitempty"`
	SortOrder          *int                   `json:"sortOrder,omitempty"`
	IsActive           *bool                  `json:"isActive,omitempty"`
}

type UpdateTierBenefitInput struct {
	ID                  string   `json:"id"`
	TierLevel           *int     `json:"tierLevel,omitempty"`
	TierName            *string  `json:"tierName,omitempty"`
	MinPoints           *int     `json:"minPoints,omitempty"`
	CashbackPercentage  *float64 `json:"cashbackPercentage,omitempty"`
	NetFee              *float64 `json:"netFee,omitempty"`
	BenefitsDescription *string  `json:"benefitsDescription,omitempty"`
	TierColor           *string  `json:"tierColor,omitempty"`
	TierIcon            *string  `json:"tierIcon,omitempty"`
	IsActive            *bool    `json:"isActive,omitempty"`
}

type UserTierInfo struct {
	UserID               string       `json:"userId"`
	Email                *string      `json:"email,omitempty"`
	CurrentTier          *TierBenefit `json:"currentTier,omitempty"`
	TotalPoints          int          `json:"totalPoints"`
	AvailableCashback    float64      `json:"availableCashback"`
	TotalCashbackClaimed float64      `json:"totalCashbackClaimed"`
	NextTier             *TierBenefit `json:"nextTier,omitempty"`
	PointsToNextTier     *int         `json:"pointsToNextTier,omitempty"`
	CreatedAt            time.Time    `json:"createdAt"`
	LastActivityAt       *time.Time   `json:"lastActivityAt,omitempty"`
}

type AgentReferralSortBy string

const (
	AgentReferralSortByCreatedAt        AgentReferralSortBy = "CREATED_AT"
	AgentReferralSortByCommissionEarned AgentReferralSortBy = "COMMISSION_EARNED"
	AgentReferralSortByVolumeUsd        AgentReferralSortBy = "VOLUME_USD"
	AgentReferralSortByTotalNodes       AgentReferralSortBy = "TOTAL_NODES"
	AgentReferralSortByActiveUsers      AgentReferralSortBy = "ACTIVE_USERS"
)

var AllAgentReferralSortBy = []AgentReferralSortBy{
	AgentReferralSortByCreatedAt,
	AgentReferralSortByCommissionEarned,
	AgentReferralSortByVolumeUsd,
	AgentReferralSortByTotalNodes,
	AgentReferralSortByActiveUsers,
}

func (e AgentReferralSortBy) IsValid() bool {
	switch e {
	case AgentReferralSortByCreatedAt, AgentReferralSortByCommissionEarned, AgentReferralSortByVolumeUsd, AgentReferralSortByTotalNodes, AgentReferralSortByActiveUsers:
		return true
	}
	return false
}

func (e AgentReferralSortBy) String() string {
	return string(e)
}

func (e *AgentReferralSortBy) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AgentReferralSortBy(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AgentReferralSortBy", str)
	}
	return nil
}

func (e AgentReferralSortBy) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *AgentReferralSortBy) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e AgentReferralSortBy) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type InfiniteAgentStatus string

const (
	InfiniteAgentStatusActive    InfiniteAgentStatus = "ACTIVE"
	InfiniteAgentStatusInactive  InfiniteAgentStatus = "INACTIVE"
	InfiniteAgentStatusSuspended InfiniteAgentStatus = "SUSPENDED"
)

var AllInfiniteAgentStatus = []InfiniteAgentStatus{
	InfiniteAgentStatusActive,
	InfiniteAgentStatusInactive,
	InfiniteAgentStatusSuspended,
}

func (e InfiniteAgentStatus) IsValid() bool {
	switch e {
	case InfiniteAgentStatusActive, InfiniteAgentStatusInactive, InfiniteAgentStatusSuspended:
		return true
	}
	return false
}

func (e InfiniteAgentStatus) String() string {
	return string(e)
}

func (e *InfiniteAgentStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = InfiniteAgentStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid InfiniteAgentStatus", str)
	}
	return nil
}

func (e InfiniteAgentStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *InfiniteAgentStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e InfiniteAgentStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type SortOrder string

const (
	SortOrderAsc  SortOrder = "ASC"
	SortOrderDesc SortOrder = "DESC"
)

var AllSortOrder = []SortOrder{
	SortOrderAsc,
	SortOrderDesc,
}

func (e SortOrder) IsValid() bool {
	switch e {
	case SortOrderAsc, SortOrderDesc:
		return true
	}
	return false
}

func (e SortOrder) String() string {
	return string(e)
}

func (e *SortOrder) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = SortOrder(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SortOrder", str)
	}
	return nil
}

func (e SortOrder) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *SortOrder) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e SortOrder) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskCategoryName string

const (
	TaskCategoryNameDaily     TaskCategoryName = "DAILY"
	TaskCategoryNameCommunity TaskCategoryName = "COMMUNITY"
	TaskCategoryNameTrading   TaskCategoryName = "TRADING"
)

var AllTaskCategoryName = []TaskCategoryName{
	TaskCategoryNameDaily,
	TaskCategoryNameCommunity,
	TaskCategoryNameTrading,
}

func (e TaskCategoryName) IsValid() bool {
	switch e {
	case TaskCategoryNameDaily, TaskCategoryNameCommunity, TaskCategoryNameTrading:
		return true
	}
	return false
}

func (e TaskCategoryName) String() string {
	return string(e)
}

func (e *TaskCategoryName) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskCategoryName(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskCategoryName", str)
	}
	return nil
}

func (e TaskCategoryName) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskCategoryName) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskCategoryName) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskFrequency string

const (
	TaskFrequencyOneTime   TaskFrequency = "ONE_TIME"
	TaskFrequencyDaily     TaskFrequency = "DAILY"
	TaskFrequencyWeekly    TaskFrequency = "WEEKLY"
	TaskFrequencyMonthly   TaskFrequency = "MONTHLY"
	TaskFrequencyUnlimited TaskFrequency = "UNLIMITED"
)

var AllTaskFrequency = []TaskFrequency{
	TaskFrequencyOneTime,
	TaskFrequencyDaily,
	TaskFrequencyWeekly,
	TaskFrequencyMonthly,
	TaskFrequencyUnlimited,
}

func (e TaskFrequency) IsValid() bool {
	switch e {
	case TaskFrequencyOneTime, TaskFrequencyDaily, TaskFrequencyWeekly, TaskFrequencyMonthly, TaskFrequencyUnlimited:
		return true
	}
	return false
}

func (e TaskFrequency) String() string {
	return string(e)
}

func (e *TaskFrequency) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskFrequency(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskFrequency", str)
	}
	return nil
}

func (e TaskFrequency) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskFrequency) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskFrequency) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskIdentifier string

const (
	TaskIdentifierDailyCheckin           TaskIdentifier = "DAILY_CHECKIN"
	TaskIdentifierMemeTradeDaily         TaskIdentifier = "MEME_TRADE_DAILY"
	TaskIdentifierPerpetualTradeDaily    TaskIdentifier = "PERPETUAL_TRADE_DAILY"
	TaskIdentifierMarketPageView         TaskIdentifier = "MARKET_PAGE_VIEW"
	TaskIdentifierConsecutiveCheckin     TaskIdentifier = "CONSECUTIVE_CHECKIN"
	TaskIdentifierConsecutiveTradingDays TaskIdentifier = "CONSECUTIVE_TRADING_DAYS"
	TaskIdentifierTwitterFollow          TaskIdentifier = "TWITTER_FOLLOW"
	TaskIdentifierTwitterRetweet         TaskIdentifier = "TWITTER_RETWEET"
	TaskIdentifierTwitterLike            TaskIdentifier = "TWITTER_LIKE"
	TaskIdentifierTelegramJoin           TaskIdentifier = "TELEGRAM_JOIN"
	TaskIdentifierInviteFriends          TaskIdentifier = "INVITE_FRIENDS"
	TaskIdentifierShareReferral          TaskIdentifier = "SHARE_REFERRAL"
	TaskIdentifierTradingPoints          TaskIdentifier = "TRADING_POINTS"
	TaskIdentifierAccumulatedTrading10k  TaskIdentifier = "ACCUMULATED_TRADING_10K"
	TaskIdentifierAccumulatedTrading50k  TaskIdentifier = "ACCUMULATED_TRADING_50K"
	TaskIdentifierAccumulatedTrading100k TaskIdentifier = "ACCUMULATED_TRADING_100K"
	TaskIdentifierAccumulatedTrading500k TaskIdentifier = "ACCUMULATED_TRADING_500K"
)

var AllTaskIdentifier = []TaskIdentifier{
	TaskIdentifierDailyCheckin,
	TaskIdentifierMemeTradeDaily,
	TaskIdentifierPerpetualTradeDaily,
	TaskIdentifierMarketPageView,
	TaskIdentifierConsecutiveCheckin,
	TaskIdentifierConsecutiveTradingDays,
	TaskIdentifierTwitterFollow,
	TaskIdentifierTwitterRetweet,
	TaskIdentifierTwitterLike,
	TaskIdentifierTelegramJoin,
	TaskIdentifierInviteFriends,
	TaskIdentifierShareReferral,
	TaskIdentifierTradingPoints,
	TaskIdentifierAccumulatedTrading10k,
	TaskIdentifierAccumulatedTrading50k,
	TaskIdentifierAccumulatedTrading100k,
	TaskIdentifierAccumulatedTrading500k,
}

func (e TaskIdentifier) IsValid() bool {
	switch e {
	case TaskIdentifierDailyCheckin, TaskIdentifierMemeTradeDaily, TaskIdentifierPerpetualTradeDaily, TaskIdentifierMarketPageView, TaskIdentifierConsecutiveCheckin, TaskIdentifierConsecutiveTradingDays, TaskIdentifierTwitterFollow, TaskIdentifierTwitterRetweet, TaskIdentifierTwitterLike, TaskIdentifierTelegramJoin, TaskIdentifierInviteFriends, TaskIdentifierShareReferral, TaskIdentifierTradingPoints, TaskIdentifierAccumulatedTrading10k, TaskIdentifierAccumulatedTrading50k, TaskIdentifierAccumulatedTrading100k, TaskIdentifierAccumulatedTrading500k:
		return true
	}
	return false
}

func (e TaskIdentifier) String() string {
	return string(e)
}

func (e *TaskIdentifier) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskIdentifier(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskIdentifier", str)
	}
	return nil
}

func (e TaskIdentifier) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskIdentifier) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskIdentifier) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
