package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	admin_resolvers "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/resolvers"
)

// CreateTask is the resolver for the createTask field.
func (r *mutationResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.CreateTask(ctx, input)
}

// UpdateTask is the resolver for the updateTask field.
func (r *mutationResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.UpdateTask(ctx, input)
}

// DeleteTask is the resolver for the deleteTask field.
func (r *mutationResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.DeleteTask(ctx, taskID)
}

// CreateTaskCategory is the resolver for the createTaskCategory field.
func (r *mutationResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.CreateTaskCategory(ctx, input)
}

// UpdateTaskCategory is the resolver for the updateTaskCategory field.
func (r *mutationResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.UpdateTaskCategory(ctx, input)
}

// DeleteTaskCategory is the resolver for the deleteTaskCategory field.
func (r *mutationResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.DeleteTaskCategory(ctx, categoryID)
}

// CreateTierBenefit is the resolver for the createTierBenefit field.
func (r *mutationResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.CreateTierBenefit(ctx, input)
}

// UpdateTierBenefit is the resolver for the updateTierBenefit field.
func (r *mutationResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.UpdateTierBenefit(ctx, input)
}

// DeleteTierBenefit is the resolver for the deleteTierBenefit field.
func (r *mutationResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.DeleteTierBenefit(ctx, tierBenefitID)
}

// AdminResetDailyTasks is the resolver for the adminResetDailyTasks field.
func (r *mutationResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminResetDailyTasks(ctx)
}

// AdminResetWeeklyTasks is the resolver for the adminResetWeeklyTasks field.
func (r *mutationResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminResetWeeklyTasks(ctx)
}

// AdminResetMonthlyTasks is the resolver for the adminResetMonthlyTasks field.
func (r *mutationResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminResetMonthlyTasks(ctx)
}

// AdminRecalculateAllUserTiers is the resolver for the adminRecalculateAllUserTiers field.
func (r *mutationResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminRecalculateAllUserTiers(ctx)
}

// AdminSeedInitialTasks is the resolver for the adminSeedInitialTasks field.
func (r *mutationResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminSeedInitialTasks(ctx)
}

// AdminCreateInfiniteAgent is the resolver for the adminCreateInfiniteAgent field.
func (r *mutationResolver) AdminCreateInfiniteAgent(ctx context.Context, input gql_model.AdminCreateInfiniteAgentInput) (*gql_model.AdminInfiniteAgentResponse, error) {
	return r.AgentReferralService.AdminCreateInfiniteAgent(ctx, input)
}

// AdminUpdateInfiniteAgent is the resolver for the adminUpdateInfiniteAgent field.
func (r *mutationResolver) AdminUpdateInfiniteAgent(ctx context.Context, input gql_model.AdminUpdateInfiniteAgentInput) (*gql_model.AdminInfiniteAgentResponse, error) {
	return r.AgentReferralService.AdminUpdateInfiniteAgent(ctx, input)
}

// AdminDeleteInfiniteAgent is the resolver for the adminDeleteInfiniteAgent field.
func (r *mutationResolver) AdminDeleteInfiniteAgent(ctx context.Context, id string) (*gql_model.AdminDeleteResponse, error) {
	return r.AgentReferralService.AdminDeleteInfiniteAgent(ctx, id)
}

// AdminToggleInfiniteAgentStatus is the resolver for the adminToggleInfiniteAgentStatus field.
func (r *mutationResolver) AdminToggleInfiniteAgentStatus(ctx context.Context, id string, status gql_model.InfiniteAgentStatus) (*gql_model.AdminInfiniteAgentResponse, error) {
	return r.AgentReferralService.AdminToggleInfiniteAgentStatus(ctx, id, status)
}

// AdminUpdateAgentLevel is the resolver for the adminUpdateAgentLevel field.
func (r *mutationResolver) AdminUpdateAgentLevel(ctx context.Context, input gql_model.AdminUpdateAgentLevelInput) (*gql_model.AdminAgentLevelResponse, error) {
	panic(fmt.Errorf("not implemented: AdminUpdateAgentLevel - adminUpdateAgentLevel"))
}

// AdminUpdateAgentLevelCommissionRates is the resolver for the adminUpdateAgentLevelCommissionRates field.
func (r *mutationResolver) AdminUpdateAgentLevelCommissionRates(ctx context.Context, input gql_model.AdminUpdateCommissionRatesInput) (*gql_model.AdminAgentLevelResponse, error) {
	panic(fmt.Errorf("not implemented: AdminUpdateAgentLevelCommissionRates - adminUpdateAgentLevelCommissionRates"))
}

// AdminUpdateAgentLevelVolumeThresholds is the resolver for the adminUpdateAgentLevelVolumeThresholds field.
func (r *mutationResolver) AdminUpdateAgentLevelVolumeThresholds(ctx context.Context, input gql_model.AdminUpdateVolumeThresholdsInput) (*gql_model.AdminAgentLevelResponse, error) {
	panic(fmt.Errorf("not implemented: AdminUpdateAgentLevelVolumeThresholds - adminUpdateAgentLevelVolumeThresholds"))
}

// AdminUpdateAgentLevelFeeRates is the resolver for the adminUpdateAgentLevelFeeRates field.
func (r *mutationResolver) AdminUpdateAgentLevelFeeRates(ctx context.Context, input gql_model.AdminUpdateFeeRatesInput) (*gql_model.AdminAgentLevelResponse, error) {
	panic(fmt.Errorf("not implemented: AdminUpdateAgentLevelFeeRates - adminUpdateAgentLevelFeeRates"))
}

// AdminCreateReferralTreeSnapshot is the resolver for the adminCreateReferralTreeSnapshot field.
func (r *mutationResolver) AdminCreateReferralTreeSnapshot(ctx context.Context, input gql_model.AdminCreateTreeSnapshotInput) (*gql_model.AdminCreateTreeSnapshotResponse, error) {
	panic(fmt.Errorf("not implemented: AdminCreateReferralTreeSnapshot - adminCreateReferralTreeSnapshot"))
}

// AdminCreateAllReferralTreeSnapshots is the resolver for the adminCreateAllReferralTreeSnapshots field.
func (r *mutationResolver) AdminCreateAllReferralTreeSnapshots(ctx context.Context) (*gql_model.AdminCreateAllTreeSnapshotsResponse, error) {
	panic(fmt.Errorf("not implemented: AdminCreateAllReferralTreeSnapshots - adminCreateAllReferralTreeSnapshots"))
}

// AdminCreateInfiniteAgentReferralTrees is the resolver for the adminCreateInfiniteAgentReferralTrees field.
func (r *mutationResolver) AdminCreateInfiniteAgentReferralTrees(ctx context.Context, input gql_model.AdminCreateInfiniteAgentTreesInput) (*gql_model.AdminCreateInfiniteAgentTreesResponse, error) {
	panic(fmt.Errorf("not implemented: AdminCreateInfiniteAgentReferralTrees - adminCreateInfiniteAgentReferralTrees"))
}

// AdminRecalculateReferralTreeStats is the resolver for the adminRecalculateReferralTreeStats field.
func (r *mutationResolver) AdminRecalculateReferralTreeStats(ctx context.Context, treeID string) (*gql_model.AdminReferralTreeResponse, error) {
	panic(fmt.Errorf("not implemented: AdminRecalculateReferralTreeStats - adminRecalculateReferralTreeStats"))
}

// AdminUpdateUserAgentLevel is the resolver for the adminUpdateUserAgentLevel field.
func (r *mutationResolver) AdminUpdateUserAgentLevel(ctx context.Context, input gql_model.AdminUpdateUserAgentLevelInput) (*gql_model.AdminUserResponse, error) {
	panic(fmt.Errorf("not implemented: AdminUpdateUserAgentLevel - adminUpdateUserAgentLevel"))
}

// AdminRecalculateUserReferralSnapshot is the resolver for the adminRecalculateUserReferralSnapshot field.
func (r *mutationResolver) AdminRecalculateUserReferralSnapshot(ctx context.Context, userID string) (*gql_model.AdminUserReferralSnapshotResponse, error) {
	panic(fmt.Errorf("not implemented: AdminRecalculateUserReferralSnapshot - adminRecalculateUserReferralSnapshot"))
}

// AdminCreateUserReferralRelationship is the resolver for the adminCreateUserReferralRelationship field.
func (r *mutationResolver) AdminCreateUserReferralRelationship(ctx context.Context, input gql_model.AdminCreateReferralInput) (*gql_model.AdminCreateReferralResponse, error) {
	panic(fmt.Errorf("not implemented: AdminCreateUserReferralRelationship - adminCreateUserReferralRelationship"))
}

// AdminDeleteUserReferralRelationship is the resolver for the adminDeleteUserReferralRelationship field.
func (r *mutationResolver) AdminDeleteUserReferralRelationship(ctx context.Context, input gql_model.AdminDeleteReferralInput) (*gql_model.AdminDeleteResponse, error) {
	panic(fmt.Errorf("not implemented: AdminDeleteUserReferralRelationship - adminDeleteUserReferralRelationship"))
}

// AdminRecalculateAllReferralSnapshots is the resolver for the adminRecalculateAllReferralSnapshots field.
func (r *mutationResolver) AdminRecalculateAllReferralSnapshots(ctx context.Context) (*gql_model.AdminSystemOperationResponse, error) {
	panic(fmt.Errorf("not implemented: AdminRecalculateAllReferralSnapshots - adminRecalculateAllReferralSnapshots"))
}

// AdminRecalculateAllInfiniteAgentCommissions is the resolver for the adminRecalculateAllInfiniteAgentCommissions field.
func (r *mutationResolver) AdminRecalculateAllInfiniteAgentCommissions(ctx context.Context) (*gql_model.AdminSystemOperationResponse, error) {
	panic(fmt.Errorf("not implemented: AdminRecalculateAllInfiniteAgentCommissions - adminRecalculateAllInfiniteAgentCommissions"))
}

// AdminSyncReferralTreeData is the resolver for the adminSyncReferralTreeData field.
func (r *mutationResolver) AdminSyncReferralTreeData(ctx context.Context) (*gql_model.AdminSystemOperationResponse, error) {
	panic(fmt.Errorf("not implemented: AdminSyncReferralTreeData - adminSyncReferralTreeData"))
}

// AdminGetAllTasks is the resolver for the adminGetAllTasks field.
func (r *queryResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetAllTasks(ctx)
}

// AdminGetTaskCompletionStats is the resolver for the adminGetTaskCompletionStats field.
func (r *queryResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetTaskCompletionStats(ctx, input)
}

// AdminGetUserActivityStats is the resolver for the adminGetUserActivityStats field.
func (r *queryResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetUserActivityStats(ctx, input)
}

// AdminGetTierDistribution is the resolver for the adminGetTierDistribution field.
func (r *queryResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetTierDistribution(ctx)
}

// AdminGetTopUsers is the resolver for the adminGetTopUsers field.
func (r *queryResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetTopUsers(ctx, limit)
}

// AdminGetAllInfiniteAgents is the resolver for the adminGetAllInfiniteAgents field.
func (r *queryResolver) AdminGetAllInfiniteAgents(ctx context.Context, input *gql_model.AdminPaginationInput) (*gql_model.AdminInfiniteAgentsResponse, error) {
	return r.AgentReferralService.AdminGetAllInfiniteAgents(ctx, input)
}

// AdminGetInfiniteAgentByID is the resolver for the adminGetInfiniteAgentById field.
func (r *queryResolver) AdminGetInfiniteAgentByID(ctx context.Context, id string) (*gql_model.AdminInfiniteAgentResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetInfiniteAgentByID - adminGetInfiniteAgentById"))
}

// AdminGetInfiniteAgentByUserID is the resolver for the adminGetInfiniteAgentByUserId field.
func (r *queryResolver) AdminGetInfiniteAgentByUserID(ctx context.Context, userID string) (*gql_model.AdminInfiniteAgentResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetInfiniteAgentByUserID - adminGetInfiniteAgentByUserId"))
}

// AdminGetAllAgentLevels is the resolver for the adminGetAllAgentLevels field.
func (r *queryResolver) AdminGetAllAgentLevels(ctx context.Context) ([]*gql_model.AdminAgentLevel, error) {
	return r.AgentReferralService.AdminGetAllAgentLevels(ctx)
}

// AdminGetAgentLevelByID is the resolver for the adminGetAgentLevelById field.
func (r *queryResolver) AdminGetAgentLevelByID(ctx context.Context, id int) (*gql_model.AdminAgentLevel, error) {
	panic(fmt.Errorf("not implemented: AdminGetAgentLevelByID - adminGetAgentLevelById"))
}

// AdminGetAllReferralTrees is the resolver for the adminGetAllReferralTrees field.
func (r *queryResolver) AdminGetAllReferralTrees(ctx context.Context, input *gql_model.AdminReferralTreesInput) (*gql_model.AdminReferralTreesResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetAllReferralTrees - adminGetAllReferralTrees"))
}

// AdminGetReferralTreeByID is the resolver for the adminGetReferralTreeById field.
func (r *queryResolver) AdminGetReferralTreeByID(ctx context.Context, id string) (*gql_model.AdminReferralTreeResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetReferralTreeByID - adminGetReferralTreeById"))
}

// AdminGetReferralTreeByRootUser is the resolver for the adminGetReferralTreeByRootUser field.
func (r *queryResolver) AdminGetReferralTreeByRootUser(ctx context.Context, rootUserID string) (*gql_model.AdminReferralTreeResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetReferralTreeByRootUser - adminGetReferralTreeByRootUser"))
}

// AdminGetInfiniteAgentReferralTrees is the resolver for the adminGetInfiniteAgentReferralTrees field.
func (r *queryResolver) AdminGetInfiniteAgentReferralTrees(ctx context.Context, input *gql_model.AdminInfiniteAgentTreesInput) (*gql_model.AdminInfiniteAgentTreesResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetInfiniteAgentReferralTrees - adminGetInfiniteAgentReferralTrees"))
}

// AdminSearchUsersByInvitationCode is the resolver for the adminSearchUsersByInvitationCode field.
func (r *queryResolver) AdminSearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*gql_model.AdminUser, error) {
	panic(fmt.Errorf("not implemented: AdminSearchUsersByInvitationCode - adminSearchUsersByInvitationCode"))
}

// AdminGetUserReferralSnapshot is the resolver for the adminGetUserReferralSnapshot field.
func (r *queryResolver) AdminGetUserReferralSnapshot(ctx context.Context, userID string) (*gql_model.AdminUserReferralSnapshotResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetUserReferralSnapshot - adminGetUserReferralSnapshot"))
}

// AdminGetUserReferralTree is the resolver for the adminGetUserReferralTree field.
func (r *queryResolver) AdminGetUserReferralTree(ctx context.Context, userID string, depth *int) (*gql_model.AdminUserReferralTreeResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetUserReferralTree - adminGetUserReferralTree"))
}

// AdminGetAgentReferralStats is the resolver for the adminGetAgentReferralStats field.
func (r *queryResolver) AdminGetAgentReferralStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminAgentReferralStatsResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetAgentReferralStats - adminGetAgentReferralStats"))
}

// AdminGetCommissionDistributionStats is the resolver for the adminGetCommissionDistributionStats field.
func (r *queryResolver) AdminGetCommissionDistributionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminCommissionDistributionResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetCommissionDistributionStats - adminGetCommissionDistributionStats"))
}

// AdminGetTopPerformingAgents is the resolver for the adminGetTopPerformingAgents field.
func (r *queryResolver) AdminGetTopPerformingAgents(ctx context.Context, input gql_model.AdminTopAgentsInput) (*gql_model.AdminTopAgentsResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetTopPerformingAgents - adminGetTopPerformingAgents"))
}

// AdminGetInfiniteAgentPerformanceStats is the resolver for the adminGetInfiniteAgentPerformanceStats field.
func (r *queryResolver) AdminGetInfiniteAgentPerformanceStats(ctx context.Context, input gql_model.AdminInfiniteAgentStatsInput) (*gql_model.AdminInfiniteAgentPerformanceResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetInfiniteAgentPerformanceStats - adminGetInfiniteAgentPerformanceStats"))
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }

// !!! WARNING !!!
// The code below was going to be deleted when updating resolvers. It has been copied here so you have
// one last chance to move it out of harms way if you want. There are two reasons this happens:
//  - When renaming or deleting a resolver the old code will be put in here. You can safely delete
//    it when you're done.
//  - You have helper methods in this file. Move them out to keep these resolver files clean.
/*
	func (r *queryResolver) AdminGetInfiniteAgentById(ctx context.Context, id string) (*gql_model.AdminInfiniteAgentResponse, error) {
	return r.AgentReferralService.AdminGetInfiniteAgentById(ctx, id)
}
func (r *queryResolver) AdminGetInfiniteAgentByUserId(ctx context.Context, userID string) (*gql_model.AdminInfiniteAgentResponse, error) {
	return r.AgentReferralService.AdminGetInfiniteAgentByUserId(ctx, userID)
}
func (r *queryResolver) AdminGetAgentLevelById(ctx context.Context, id int) (*gql_model.AdminAgentLevel, error) {
	return r.AgentReferralService.AdminGetAgentLevelById(ctx, id)
}
*/
