# Agent Referral Admin GraphQL Schema

# ===== ENUMS =====
enum InfiniteAgentStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum AgentReferralSortBy {
  CREATED_AT
  COMMISSION_EARNED
  VOLUME_USD
  TOTAL_NODES
  ACTIVE_USERS
}

enum SortOrder {
  ASC
  DESC
}

# ===== INPUT TYPES =====

# Pagination Input
input AdminPaginationInput {
  page: Int = 1
  pageSize: Int = 20
  sortBy: String
  sortOrder: SortOrder = DESC
}

# Infinite Agent Inputs
input AdminCreateInfiniteAgentInput {
  userId: ID!
  commissionRateN: Float!
  status: InfiniteAgentStatus = ACTIVE
  description: String
}

input AdminUpdateInfiniteAgentInput {
  id: ID!
  commissionRateN: Float
  status: InfiniteAgentStatus
  description: String
}

# Agent Level Inputs
input AdminUpdateAgentLevelInput {
  id: Int!
  name: String
  memeVolumeThreshold: Float
  contractVolumeThreshold: Float
  memeFeeRate: Float
  takerFeeRate: Float
  makerFeeRate: Float
  directCommissionRate: Float
  indirectCommissionRate: Float
  extendedCommissionRate: Float
  memeFeeRebate: Float
}

input AdminUpdateCommissionRatesInput {
  levelId: Int!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
}

input AdminUpdateVolumeThresholdsInput {
  levelId: Int!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
}

input AdminUpdateFeeRatesInput {
  levelId: Int!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  memeFeeRebate: Float!
}

# Referral Tree Inputs
input AdminReferralTreesInput {
  page: Int = 1
  pageSize: Int = 20
  rootUserId: ID
  hasInfiniteAgent: Boolean
  startDate: Time
  endDate: Time
  sortBy: AgentReferralSortBy = CREATED_AT
  sortOrder: SortOrder = DESC
}

input AdminCreateTreeSnapshotInput {
  rootUserId: ID!
  snapshotDate: Time
  description: String
}

input AdminInfiniteAgentTreesInput {
  page: Int = 1
  pageSize: Int = 20
  infiniteAgentUserId: ID
  startDate: Time
  endDate: Time
  sortBy: AgentReferralSortBy = CREATED_AT
  sortOrder: SortOrder = DESC
}

input AdminCreateInfiniteAgentTreesInput {
  infiniteAgentUserIds: [ID!]
  snapshotDate: Time
  forceRecalculate: Boolean = false
}

# User Management Inputs
input AdminUpdateUserAgentLevelInput {
  userId: ID!
  agentLevelId: Int!
  reason: String
}

input AdminCreateReferralInput {
  userId: ID!
  referrerId: ID!
  depth: Int = 1
}

input AdminDeleteReferralInput {
  userId: ID!
  referrerId: ID!
}

# Statistics Inputs
input AdminTopAgentsInput {
  limit: Int = 10
  sortBy: AgentReferralSortBy = COMMISSION_EARNED
  startDate: Time
  endDate: Time
  agentLevelId: Int
}

input AdminInfiniteAgentStatsInput {
  infiniteAgentUserId: ID
  startDate: Time!
  endDate: Time!
  includeTreeBreakdown: Boolean = false
}

# ===== RESPONSE TYPES =====

# Common Response Types
type AdminDeleteResponse {
  success: Boolean!
  message: String!
}

type AdminSystemOperationResponse {
  success: Boolean!
  message: String!
  processedCount: Int!
  errorCount: Int!
  duration: String!
}

# Infinite Agent Types
type AdminInfiniteAgent {
  id: ID!
  userId: ID!
  user: AdminUser!
  commissionRateN: Float!
  totalNetFeeUSD: Float!
  totalStandardCommissionPaidUSD: Float!
  finalCommissionAmountUSD: Float!
  memeTotalFeeUSD: Float!
  memePaidCommissionUSD: Float!
  memeNetFeeUSD: Float!
  memeActivityCashbackUSD: Float!
  contractTotalFeeUSD: Float!
  contractPaidCommissionUSD: Float!
  contractNetFeeUSD: Float!
  status: String!
  description: String
  createdAt: Time!
  updatedAt: Time!
  lastCalculatedAt: Time
}

type AdminInfiniteAgentResponse {
  infiniteAgent: AdminInfiniteAgent
  success: Boolean!
  message: String!
}

type AdminInfiniteAgentsResponse {
  infiniteAgents: [AdminInfiniteAgent!]!
  totalCount: Int!
  currentPage: Int!
  totalPages: Int!
  success: Boolean!
  message: String!
}

# Agent Level Types
type AdminAgentLevel {
  id: Int!
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
  userCount: Int!
  activeUserCount: Int!
  totalVolumeUSD: Float!
  totalCommissionPaidUSD: Float!
}

type AdminAgentLevelResponse {
  agentLevel: AdminAgentLevel
  success: Boolean!
  message: String!
}

# User Types
type AdminUser {
  id: ID!
  email: String
  invitationCode: String
  agentLevelId: Int!
  agentLevel: AdminAgentLevel!
  createdAt: Time!
  updatedAt: Time!
  firstTransactionAt: Time
  levelUpgradedAt: Time
  levelGracePeriodStartedAt: Time
  isInfiniteAgent: Boolean!
  referralSnapshot: AdminReferralSnapshot
}

type AdminUserResponse {
  user: AdminUser
  success: Boolean!
  message: String!
}

type AdminReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  tradingUserCount: Int!
  totalVolumeUSD: Float!
  totalRewardsDistributed: Float!
  totalPerpsVolumeUSD: Float!
  totalPerpsFees: Float!
  totalPerpsFeesPaid: Float!
  totalPerpsFeesUnPaid: Float!
  totalPerpsTradesCount: Int!
  totalMemeVolumeUSD: Float!
  totalMemeFees: Float!
  totalMemeFeesPaid: Float!
  totalMemeFeesUnPaid: Float!
  totalMemeTradesCount: Int!
  totalCommissionEarnedUSD: Float!
  claimedCommissionUSD: Float!
  unclaimedCommissionUSD: Float!
  totalCashbackEarnedUSD: Float!
  claimedCashbackUSD: Float!
  unclaimedCashbackUSD: Float!
  lastUpdatedAt: Time!
}

type AdminUserReferralSnapshotResponse {
  snapshot: AdminReferralSnapshot
  success: Boolean!
  message: String!
}

type AdminCreateReferralResponse {
  success: Boolean!
  message: String!
  createdRelationships: Int!
}

# Referral Tree Types
type AdminReferralTree {
  id: ID!
  rootUserId: ID!
  rootUser: AdminUser!
  snapshotDate: Time!
  totalNodes: Int!
  maxDepth: Int!
  directCount: Int!
  activeUsers: Int!
  tradingUsers: Int!
  infiniteAgentUserId: ID
  infiniteAgentUser: AdminUser
  hasInfiniteAgent: Boolean!
  description: String
  isValid: Boolean!
  createdAt: Time!
  totalVolumeUSD: Float!
  totalCommissionPaidUSD: Float!
  nodes: [AdminReferralTreeNode!]!
}

type AdminReferralTreeNode {
  id: ID!
  treeSnapshotId: ID!
  userId: ID!
  user: AdminUser!
  parentUserId: ID
  parentUser: AdminUser
  referrerId: ID
  referrer: AdminUser
  depth: Int!
  level: Int!
  position: Int!
  isActive: Boolean!
  isTrading: Boolean!
  agentLevelId: Int!
  agentLevel: AdminAgentLevel!
  volumeUSD: Float!
  commissionEarnedUSD: Float!
  createdAt: Time!
}

type AdminReferralTreeResponse {
  tree: AdminReferralTree
  success: Boolean!
  message: String!
}

type AdminReferralTreesResponse {
  trees: [AdminReferralTree!]!
  totalCount: Int!
  currentPage: Int!
  totalPages: Int!
  success: Boolean!
  message: String!
}

type AdminCreateTreeSnapshotResponse {
  success: Boolean!
  message: String!
  treeId: ID
  processedNodes: Int!
}

type AdminCreateAllTreeSnapshotsResponse {
  success: Boolean!
  message: String!
  processedTrees: Int!
  errorCount: Int!
  totalNodes: Int!
}

# Infinite Agent Tree Types
type AdminInfiniteAgentTree {
  id: ID!
  infiniteAgentUserId: ID!
  infiniteAgentUser: AdminUser!
  commissionRateN: Float!
  rootUserId: ID!
  rootUser: AdminUser!
  snapshotDate: Time!
  totalNodes: Int!
  maxDepth: Int!
  directCount: Int!
  activeUsers: Int!
  tradingUsers: Int!
  totalCommissionEarned: Float!
  totalVolumeUsd: Float!
  status: String!
  description: String
  createdAt: Time!
  treeNodes: [AdminInfiniteAgentTreeNode!]!
}

type AdminInfiniteAgentTreeNode {
  id: ID!
  treeId: ID!
  userId: ID!
  user: AdminUser!
  parentUserId: ID
  parentUser: AdminUser
  referrerId: ID
  referrer: AdminUser
  depth: Int!
  level: Int!
  position: Int!
  isActive: Boolean!
  isTrading: Boolean!
  agentLevelId: Int!
  agentLevel: AdminAgentLevel!
  commissionEarned: Float!
  volumeUsd: Float!
  feeVolumeUsd: Float!
  createdAt: Time!
}

type AdminInfiniteAgentTreesResponse {
  trees: [AdminInfiniteAgentTree!]!
  totalCount: Int!
  currentPage: Int!
  totalPages: Int!
  success: Boolean!
  message: String!
}

type AdminCreateInfiniteAgentTreesResponse {
  success: Boolean!
  message: String!
  processedCount: Int!
  errorCount: Int!
  totalInfiniteAgents: Int!
  snapshotDate: String!
}

# User Referral Tree Types
type AdminUserReferralTreeResponse {
  userId: ID!
  user: AdminUser!
  referralTree: [AdminUserReferralNode!]!
  totalNodes: Int!
  maxDepth: Int!
  directCount: Int!
  activeUsers: Int!
  tradingUsers: Int!
  totalVolumeUSD: Float!
  totalCommissionEarnedUSD: Float!
  success: Boolean!
  message: String!
}

type AdminUserReferralNode {
  userId: ID!
  user: AdminUser!
  parentUserId: ID
  parentUser: AdminUser
  depth: Int!
  level: Int!
  isActive: Boolean!
  isTrading: Boolean!
  agentLevelId: Int!
  agentLevel: AdminAgentLevel!
  directReferrals: Int!
  totalDownline: Int!
  volumeUSD: Float!
  commissionEarnedUSD: Float!
  children: [AdminUserReferralNode!]!
}

# Statistics Types
type AdminAgentReferralStatsResponse {
  totalUsers: Int!
  totalActiveUsers: Int!
  totalTradingUsers: Int!
  totalInfiniteAgents: Int!
  totalReferralTrees: Int!
  totalVolumeUSD: Float!
  totalCommissionPaidUSD: Float!
  totalCashbackPaidUSD: Float!
  averageTreeDepth: Float!
  averageDirectReferrals: Float!
  levelDistribution: [AdminLevelDistribution!]!
  dailyStats: [AdminDailyAgentStats!]!
  success: Boolean!
  message: String!
}

type AdminLevelDistribution {
  levelId: Int!
  levelName: String!
  userCount: Int!
  percentage: Float!
  totalVolumeUSD: Float!
  totalCommissionPaidUSD: Float!
}

type AdminDailyAgentStats {
  date: String!
  newUsers: Int!
  activeUsers: Int!
  tradingUsers: Int!
  volumeUSD: Float!
  commissionPaidUSD: Float!
  cashbackPaidUSD: Float!
}

type AdminCommissionDistributionResponse {
  totalCommissionPaidUSD: Float!
  directCommissionUSD: Float!
  indirectCommissionUSD: Float!
  extendedCommissionUSD: Float!
  infiniteAgentCommissionUSD: Float!
  levelBreakdown: [AdminCommissionLevelBreakdown!]!
  dailyDistribution: [AdminDailyCommissionStats!]!
  success: Boolean!
  message: String!
}

type AdminCommissionLevelBreakdown {
  levelId: Int!
  levelName: String!
  totalCommissionUSD: Float!
  directCommissionUSD: Float!
  indirectCommissionUSD: Float!
  extendedCommissionUSD: Float!
  userCount: Int!
}

type AdminDailyCommissionStats {
  date: String!
  totalCommissionUSD: Float!
  directCommissionUSD: Float!
  indirectCommissionUSD: Float!
  extendedCommissionUSD: Float!
  infiniteAgentCommissionUSD: Float!
}

type AdminTopAgentsResponse {
  agents: [AdminTopAgent!]!
  totalCount: Int!
  success: Boolean!
  message: String!
}

type AdminTopAgent {
  userId: ID!
  user: AdminUser!
  agentLevelId: Int!
  agentLevel: AdminAgentLevel!
  totalVolumeUSD: Float!
  totalCommissionEarnedUSD: Float!
  directReferrals: Int!
  totalDownline: Int!
  tradingUsers: Int!
  rank: Int!
  isInfiniteAgent: Boolean!
}

type AdminInfiniteAgentPerformanceResponse {
  infiniteAgentUserId: ID
  infiniteAgentUser: AdminUser
  totalCommissionEarnedUSD: Float!
  totalNetFeeUSD: Float!
  totalStandardCommissionPaidUSD: Float!
  finalCommissionAmountUSD: Float!
  commissionRateN: Float!
  treeCount: Int!
  totalNodes: Int!
  activeUsers: Int!
  tradingUsers: Int!
  totalVolumeUSD: Float!
  memeBreakdown: AdminInfiniteAgentMemeBreakdown!
  contractBreakdown: AdminInfiniteAgentContractBreakdown!
  dailyPerformance: [AdminDailyInfiniteAgentStats!]!
  treeBreakdown: [AdminInfiniteAgentTreeBreakdown!]!
  success: Boolean!
  message: String!
}

type AdminInfiniteAgentMemeBreakdown {
  totalFeeUSD: Float!
  paidCommissionUSD: Float!
  netFeeUSD: Float!
  activityCashbackUSD: Float!
  transactionCount: Int!
  volumeUSD: Float!
}

type AdminInfiniteAgentContractBreakdown {
  totalFeeUSD: Float!
  paidCommissionUSD: Float!
  netFeeUSD: Float!
  transactionCount: Int!
  volumeUSD: Float!
}

type AdminDailyInfiniteAgentStats {
  date: String!
  commissionEarnedUSD: Float!
  netFeeUSD: Float!
  volumeUSD: Float!
  activeUsers: Int!
  tradingUsers: Int!
  newNodes: Int!
}

type AdminInfiniteAgentTreeBreakdown {
  treeId: ID!
  rootUserId: ID!
  rootUser: AdminUser!
  totalNodes: Int!
  activeUsers: Int!
  tradingUsers: Int!
  volumeUSD: Float!
  commissionEarnedUSD: Float!
  netFeeUSD: Float!
  snapshotDate: Time!
}
