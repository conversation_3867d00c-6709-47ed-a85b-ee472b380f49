scalar Time
scalar Int64

directive @adminAuth on FIELD_DEFINITION

type Query {
  # Admin: Get all tasks
  adminGetAllTasks: [ActivityTask!]! @adminAuth

  # Admin: Get task completion statistics
  adminGetTaskCompletionStats(input: AdminStatsInput!): AdminTaskCompletionStatsResponse! @adminAuth

  # Admin: Get user activity statistics
  adminGetUserActivityStats(input: AdminStatsInput!): AdminUserActivityStatsResponse! @adminAuth

  # Admin: Get tier distribution
  adminGetTierDistribution: AdminTierDistributionResponse! @adminAuth

  # Admin: Get top users by points
  adminGetTopUsers(limit: Int = 10): [UserTierInfo!]! @adminAuth

  # ===== AGENT REFERRAL ADMIN APIs =====

  # Infinite Agent Management
  adminGetAllInfiniteAgents(input: AdminPaginationInput): AdminInfiniteAgentsResponse! @adminAuth
  adminGetInfiniteAgentById(id: ID!): AdminInfiniteAgentResponse! @adminAuth
  adminGetInfiniteAgentByUserId(userId: ID!): AdminInfiniteAgentResponse! @adminAuth

  # Agent Level Management
  adminGetAllAgentLevels: [AdminAgentLevel!]! @adminAuth
  adminGetAgentLevelById(id: Int!): AdminAgentLevel @adminAuth

  # Referral Tree Management
  adminGetAllReferralTrees(input: AdminReferralTreesInput): AdminReferralTreesResponse! @adminAuth
  adminGetReferralTreeById(id: ID!): AdminReferralTreeResponse! @adminAuth
  adminGetReferralTreeByRootUser(rootUserId: ID!): AdminReferralTreeResponse! @adminAuth
  adminGetInfiniteAgentReferralTrees(input: AdminInfiniteAgentTreesInput): AdminInfiniteAgentTreesResponse! @adminAuth

  # User Management
  adminSearchUsersByInvitationCode(invitationCode: String!): [AdminUser!]! @adminAuth
  adminGetUserReferralSnapshot(userId: ID!): AdminUserReferralSnapshotResponse! @adminAuth
  adminGetUserReferralTree(userId: ID!, depth: Int = 3): AdminUserReferralTreeResponse! @adminAuth

  # Statistics & Reports
  adminGetAgentReferralStats(input: AdminStatsInput!): AdminAgentReferralStatsResponse! @adminAuth
  adminGetCommissionDistributionStats(input: AdminStatsInput!): AdminCommissionDistributionResponse! @adminAuth
  adminGetTopPerformingAgents(input: AdminTopAgentsInput!): AdminTopAgentsResponse! @adminAuth
  adminGetInfiniteAgentPerformanceStats(input: AdminInfiniteAgentStatsInput!): AdminInfiniteAgentPerformanceResponse! @adminAuth
}

type Mutation {
  # Admin: Create task
  createTask(input: CreateTaskInput!): ActivityTask! @adminAuth

  # Admin: Update task
  updateTask(input: UpdateTaskInput!): ActivityTask! @adminAuth

  # Admin: Delete task
  deleteTask(taskId: ID!): Boolean! @adminAuth

  # Admin: Create task category
  createTaskCategory(input: CreateTaskCategoryInput!): TaskCategory! @adminAuth

  # Admin: Update task category
  updateTaskCategory(input: UpdateTaskCategoryInput!): TaskCategory! @adminAuth

  # Admin: Delete task category
  deleteTaskCategory(categoryId: ID!): Boolean! @adminAuth

  # Admin: Create tier benefit
  createTierBenefit(input: CreateTierBenefitInput!): TierBenefitResponse! @adminAuth

  # Admin: Update tier benefit
  updateTierBenefit(input: UpdateTierBenefitInput!): TierBenefitResponse! @adminAuth

  # Admin: Delete tier benefit
  deleteTierBenefit(tierBenefitId: ID!): Boolean! @adminAuth

  # Admin: Reset daily tasks
  adminResetDailyTasks: Boolean! @adminAuth

  # Admin: Reset weekly tasks
  adminResetWeeklyTasks: Boolean! @adminAuth

  # Admin: Reset monthly tasks
  adminResetMonthlyTasks: Boolean! @adminAuth

  # Admin: Recalculate all user tiers
  adminRecalculateAllUserTiers: Boolean! @adminAuth

  # Admin: Seed initial tasks
  adminSeedInitialTasks: Boolean! @adminAuth

  # ===== AGENT REFERRAL ADMIN MUTATIONS =====

  # Infinite Agent Management
  adminCreateInfiniteAgent(input: AdminCreateInfiniteAgentInput!): AdminInfiniteAgentResponse! @adminAuth
  adminUpdateInfiniteAgent(input: AdminUpdateInfiniteAgentInput!): AdminInfiniteAgentResponse! @adminAuth
  adminDeleteInfiniteAgent(id: ID!): AdminDeleteResponse! @adminAuth
  adminToggleInfiniteAgentStatus(id: ID!, status: InfiniteAgentStatus!): AdminInfiniteAgentResponse! @adminAuth

  # Agent Level Management
  adminUpdateAgentLevel(input: AdminUpdateAgentLevelInput!): AdminAgentLevelResponse! @adminAuth
  adminUpdateAgentLevelCommissionRates(input: AdminUpdateCommissionRatesInput!): AdminAgentLevelResponse! @adminAuth
  adminUpdateAgentLevelVolumeThresholds(input: AdminUpdateVolumeThresholdsInput!): AdminAgentLevelResponse! @adminAuth
  adminUpdateAgentLevelFeeRates(input: AdminUpdateFeeRatesInput!): AdminAgentLevelResponse! @adminAuth

  # Referral Tree Management
  adminCreateReferralTreeSnapshot(input: AdminCreateTreeSnapshotInput!): AdminCreateTreeSnapshotResponse! @adminAuth
  adminCreateAllReferralTreeSnapshots: AdminCreateAllTreeSnapshotsResponse! @adminAuth
  adminCreateInfiniteAgentReferralTrees(input: AdminCreateInfiniteAgentTreesInput!): AdminCreateInfiniteAgentTreesResponse! @adminAuth
  adminRecalculateReferralTreeStats(treeId: ID!): AdminReferralTreeResponse! @adminAuth

  # User Management
  adminUpdateUserAgentLevel(input: AdminUpdateUserAgentLevelInput!): AdminUserResponse! @adminAuth
  adminRecalculateUserReferralSnapshot(userId: ID!): AdminUserReferralSnapshotResponse! @adminAuth
  adminCreateUserReferralRelationship(input: AdminCreateReferralInput!): AdminCreateReferralResponse! @adminAuth
  adminDeleteUserReferralRelationship(input: AdminDeleteReferralInput!): AdminDeleteResponse! @adminAuth

  # System Operations
  adminRecalculateAllReferralSnapshots: AdminSystemOperationResponse! @adminAuth
  adminRecalculateAllInfiniteAgentCommissions: AdminSystemOperationResponse! @adminAuth
  adminSyncReferralTreeData: AdminSystemOperationResponse! @adminAuth
}
