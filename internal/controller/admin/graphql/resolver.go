package graphql

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/resolvers"
	user_resolvers "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	ActivityCashbackService *user_resolvers.ActivityCashbackResolver
	AgentReferralService    *resolvers.AdminAgentReferralResolver
}

func NewAdminRootResolver() *Resolver {
	return &Resolver{
		ActivityCashbackService: user_resolvers.NewActivityCashbackResolver(),
		AgentReferralService:    resolvers.NewAdminAgentReferralResolver(),
	}
}
