package agent_referral_admin

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral_admin"
)

// AdminAgentReferralService implements AdminAgentReferralServiceInterface
type AdminAgentReferralService struct {
	infiniteAgentRepo agent_referral_admin.InfiniteAgentRepositoryInterface
	agentLevelRepo    agent_referral_admin.AgentLevelRepositoryInterface
	referralTreeRepo  agent_referral_admin.ReferralTreeRepositoryInterface
	userRepo          agent_referral_admin.UserRepositoryInterface
	statisticsRepo    agent_referral_admin.StatisticsRepositoryInterface
}

// NewAdminAgentReferralService creates a new AdminAgentReferralService
func NewAdminAgentReferralService() AdminAgentReferralServiceInterface {
	return &AdminAgentReferralService{
		infiniteAgentRepo: agent_referral_admin.NewInfiniteAgentRepository(),
		agentLevelRepo:    agent_referral_admin.NewAgentLevelRepository(),
		referralTreeRepo:  agent_referral_admin.NewReferralTreeRepository(),
		userRepo:          agent_referral_admin.NewUserRepository(),
		statisticsRepo:    agent_referral_admin.NewStatisticsRepository(),
	}
}

// ===== INFINITE AGENT MANAGEMENT =====

func (s *AdminAgentReferralService) GetAllInfiniteAgents(ctx context.Context, page, pageSize int, sortBy, sortOrder string) (*InfiniteAgentsResult, error) {
	global.GVA_LOG.Info("Getting all infinite agents",
		zap.Int("page", page),
		zap.Int("pageSize", pageSize),
		zap.String("sortBy", sortBy),
		zap.String("sortOrder", sortOrder))

	agents, totalCount, err := s.infiniteAgentRepo.GetAllWithPagination(ctx, page, pageSize, sortBy, sortOrder)
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agents", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agents: %w", err)
	}

	totalPages := (totalCount + pageSize - 1) / pageSize

	return &InfiniteAgentsResult{
		InfiniteAgents: agents,
		TotalCount:     totalCount,
		CurrentPage:    page,
		TotalPages:     totalPages,
	}, nil
}

func (s *AdminAgentReferralService) GetInfiniteAgentById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	global.GVA_LOG.Info("Getting infinite agent by ID", zap.String("id", id.String()))

	agent, err := s.infiniteAgentRepo.GetByID(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent by ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent by ID: %w", err)
	}

	return agent, nil
}

func (s *AdminAgentReferralService) GetInfiniteAgentByUserId(ctx context.Context, userId uuid.UUID) (*model.InfiniteAgentConfig, error) {
	global.GVA_LOG.Info("Getting infinite agent by user ID", zap.String("userId", userId.String()))

	agent, err := s.infiniteAgentRepo.GetByUserID(ctx, userId)
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent by user ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent by user ID: %w", err)
	}

	return agent, nil
}

func (s *AdminAgentReferralService) CreateInfiniteAgent(ctx context.Context, input *CreateInfiniteAgentInput) (*model.InfiniteAgentConfig, error) {
	global.GVA_LOG.Info("Creating infinite agent",
		zap.String("userId", input.UserID.String()),
		zap.Float64("commissionRateN", input.CommissionRateN),
		zap.String("status", input.Status))

	// Check if user already has infinite agent config
	existing, err := s.infiniteAgentRepo.GetByUserID(ctx, input.UserID)
	if err != nil && err != gorm.ErrRecordNotFound {
		global.GVA_LOG.Error("Failed to check existing infinite agent", zap.Error(err))
		return nil, fmt.Errorf("failed to check existing infinite agent: %w", err)
	}

	if existing != nil {
		return nil, fmt.Errorf("user already has infinite agent configuration")
	}

	// Create new infinite agent config
	agent := &model.InfiniteAgentConfig{
		UserID:          input.UserID,
		CommissionRateN: decimal.NewFromFloat(input.CommissionRateN),
		Status:          input.Status,
	}

	if err := s.infiniteAgentRepo.Create(ctx, agent); err != nil {
		global.GVA_LOG.Error("Failed to create infinite agent", zap.Error(err))
		return nil, fmt.Errorf("failed to create infinite agent: %w", err)
	}

	global.GVA_LOG.Info("Infinite agent created successfully", zap.String("id", agent.ID.String()))
	return agent, nil
}

func (s *AdminAgentReferralService) UpdateInfiniteAgent(ctx context.Context, input *UpdateInfiniteAgentInput) (*model.InfiniteAgentConfig, error) {
	global.GVA_LOG.Info("Updating infinite agent", zap.String("id", input.ID.String()))

	agent, err := s.infiniteAgentRepo.GetByID(ctx, input.ID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent for update", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent for update: %w", err)
	}

	// Update fields if provided
	if input.CommissionRateN != nil {
		agent.CommissionRateN = decimal.NewFromFloat(*input.CommissionRateN)
	}

	if input.Status != nil {
		agent.Status = *input.Status
	}

	if err := s.infiniteAgentRepo.Update(ctx, agent); err != nil {
		global.GVA_LOG.Error("Failed to update infinite agent", zap.Error(err))
		return nil, fmt.Errorf("failed to update infinite agent: %w", err)
	}

	global.GVA_LOG.Info("Infinite agent updated successfully", zap.String("id", agent.ID.String()))
	return agent, nil
}

func (s *AdminAgentReferralService) DeleteInfiniteAgent(ctx context.Context, id uuid.UUID) error {
	global.GVA_LOG.Info("Deleting infinite agent", zap.String("id", id.String()))

	if err := s.infiniteAgentRepo.Delete(ctx, id); err != nil {
		global.GVA_LOG.Error("Failed to delete infinite agent", zap.Error(err))
		return fmt.Errorf("failed to delete infinite agent: %w", err)
	}

	global.GVA_LOG.Info("Infinite agent deleted successfully", zap.String("id", id.String()))
	return nil
}

func (s *AdminAgentReferralService) ToggleInfiniteAgentStatus(ctx context.Context, id uuid.UUID, status string) (*model.InfiniteAgentConfig, error) {
	global.GVA_LOG.Info("Toggling infinite agent status",
		zap.String("id", id.String()),
		zap.String("status", status))

	agent, err := s.infiniteAgentRepo.GetByID(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent for status toggle", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent for status toggle: %w", err)
	}

	agent.Status = status

	if err := s.infiniteAgentRepo.Update(ctx, agent); err != nil {
		global.GVA_LOG.Error("Failed to update infinite agent status", zap.Error(err))
		return nil, fmt.Errorf("failed to update infinite agent status: %w", err)
	}

	global.GVA_LOG.Info("Infinite agent status updated successfully",
		zap.String("id", agent.ID.String()),
		zap.String("newStatus", status))
	return agent, nil
}

// ===== AGENT LEVEL MANAGEMENT =====

func (s *AdminAgentReferralService) GetAllAgentLevels(ctx context.Context) ([]*AgentLevelWithStats, error) {
	global.GVA_LOG.Info("Getting all agent levels with statistics")

	levels, err := s.agentLevelRepo.GetAllWithStats(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get agent levels with stats", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent levels with stats: %w", err)
	}

	return levels, nil
}

func (s *AdminAgentReferralService) GetAgentLevelById(ctx context.Context, id int) (*AgentLevelWithStats, error) {
	global.GVA_LOG.Info("Getting agent level by ID", zap.Int("id", id))

	level, err := s.agentLevelRepo.GetByIDWithStats(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("Failed to get agent level by ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent level by ID: %w", err)
	}

	return level, nil
}

func (s *AdminAgentReferralService) UpdateAgentLevel(ctx context.Context, input *UpdateAgentLevelInput) (*model.AgentLevel, error) {
	global.GVA_LOG.Info("Updating agent level", zap.Int("id", input.ID))

	level, err := s.agentLevelRepo.GetByID(ctx, input.ID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get agent level for update", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent level for update: %w", err)
	}

	// Update fields if provided
	if input.Name != nil {
		level.Name = *input.Name
	}
	if input.MemeVolumeThreshold != nil {
		level.MemeVolumeThreshold = decimal.NewFromFloat(*input.MemeVolumeThreshold)
	}
	if input.ContractVolumeThreshold != nil {
		level.ContractVolumeThreshold = decimal.NewFromFloat(*input.ContractVolumeThreshold)
	}
	if input.MemeFeeRate != nil {
		level.MemeFeeRate = decimal.NewFromFloat(*input.MemeFeeRate)
	}
	if input.TakerFeeRate != nil {
		level.TakerFeeRate = decimal.NewFromFloat(*input.TakerFeeRate)
	}
	if input.MakerFeeRate != nil {
		level.MakerFeeRate = decimal.NewFromFloat(*input.MakerFeeRate)
	}
	if input.DirectCommissionRate != nil {
		level.DirectCommissionRate = decimal.NewFromFloat(*input.DirectCommissionRate)
	}
	if input.IndirectCommissionRate != nil {
		level.IndirectCommissionRate = decimal.NewFromFloat(*input.IndirectCommissionRate)
	}
	if input.ExtendedCommissionRate != nil {
		level.ExtendedCommissionRate = decimal.NewFromFloat(*input.ExtendedCommissionRate)
	}
	if input.MemeFeeRebate != nil {
		level.MemeFeeRebate = decimal.NewFromFloat(*input.MemeFeeRebate)
	}

	if err := s.agentLevelRepo.Update(ctx, level); err != nil {
		global.GVA_LOG.Error("Failed to update agent level", zap.Error(err))
		return nil, fmt.Errorf("failed to update agent level: %w", err)
	}

	global.GVA_LOG.Info("Agent level updated successfully", zap.Int("id", level.ID))
	return level, nil
}

func (s *AdminAgentReferralService) UpdateAgentLevelCommissionRates(ctx context.Context, input *UpdateCommissionRatesInput) (*model.AgentLevel, error) {
	global.GVA_LOG.Info("Updating agent level commission rates", zap.Int("levelId", input.LevelID))

	level, err := s.agentLevelRepo.GetByID(ctx, input.LevelID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get agent level for commission rate update", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent level for commission rate update: %w", err)
	}

	level.DirectCommissionRate = decimal.NewFromFloat(input.DirectCommissionRate)
	level.IndirectCommissionRate = decimal.NewFromFloat(input.IndirectCommissionRate)
	level.ExtendedCommissionRate = decimal.NewFromFloat(input.ExtendedCommissionRate)

	if err := s.agentLevelRepo.Update(ctx, level); err != nil {
		global.GVA_LOG.Error("Failed to update agent level commission rates", zap.Error(err))
		return nil, fmt.Errorf("failed to update agent level commission rates: %w", err)
	}

	global.GVA_LOG.Info("Agent level commission rates updated successfully", zap.Int("levelId", level.ID))
	return level, nil
}

func (s *AdminAgentReferralService) UpdateAgentLevelVolumeThresholds(ctx context.Context, input *UpdateVolumeThresholdsInput) (*model.AgentLevel, error) {
	global.GVA_LOG.Info("Updating agent level volume thresholds", zap.Int("levelId", input.LevelID))

	level, err := s.agentLevelRepo.GetByID(ctx, input.LevelID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get agent level for volume threshold update", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent level for volume threshold update: %w", err)
	}

	level.MemeVolumeThreshold = decimal.NewFromFloat(input.MemeVolumeThreshold)
	level.ContractVolumeThreshold = decimal.NewFromFloat(input.ContractVolumeThreshold)

	if err := s.agentLevelRepo.Update(ctx, level); err != nil {
		global.GVA_LOG.Error("Failed to update agent level volume thresholds", zap.Error(err))
		return nil, fmt.Errorf("failed to update agent level volume thresholds: %w", err)
	}

	global.GVA_LOG.Info("Agent level volume thresholds updated successfully", zap.Int("levelId", level.ID))
	return level, nil
}

func (s *AdminAgentReferralService) UpdateAgentLevelFeeRates(ctx context.Context, input *UpdateFeeRatesInput) (*model.AgentLevel, error) {
	global.GVA_LOG.Info("Updating agent level fee rates", zap.Int("levelId", input.LevelID))

	level, err := s.agentLevelRepo.GetByID(ctx, input.LevelID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get agent level for fee rate update", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent level for fee rate update: %w", err)
	}

	level.MemeFeeRate = decimal.NewFromFloat(input.MemeFeeRate)
	level.TakerFeeRate = decimal.NewFromFloat(input.TakerFeeRate)
	level.MakerFeeRate = decimal.NewFromFloat(input.MakerFeeRate)
	level.MemeFeeRebate = decimal.NewFromFloat(input.MemeFeeRebate)

	if err := s.agentLevelRepo.Update(ctx, level); err != nil {
		global.GVA_LOG.Error("Failed to update agent level fee rates", zap.Error(err))
		return nil, fmt.Errorf("failed to update agent level fee rates: %w", err)
	}

	global.GVA_LOG.Info("Agent level fee rates updated successfully", zap.Int("levelId", level.ID))
	return level, nil
}

// ===== REFERRAL TREE MANAGEMENT =====

func (s *AdminAgentReferralService) GetAllReferralTrees(ctx context.Context, input *GetReferralTreesInput) (*ReferralTreesResult, error) {
	global.GVA_LOG.Info("Getting all referral trees",
		zap.Int("page", input.Page),
		zap.Int("pageSize", input.PageSize))

	trees, totalCount, err := s.referralTreeRepo.GetAllWithPagination(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to get referral trees", zap.Error(err))
		return nil, fmt.Errorf("failed to get referral trees: %w", err)
	}

	totalPages := (totalCount + input.PageSize - 1) / input.PageSize

	return &ReferralTreesResult{
		Trees:       trees,
		TotalCount:  totalCount,
		CurrentPage: input.Page,
		TotalPages:  totalPages,
	}, nil
}

func (s *AdminAgentReferralService) GetReferralTreeById(ctx context.Context, id uint) (*ReferralTreeWithNodes, error) {
	global.GVA_LOG.Info("Getting referral tree by ID", zap.Uint("id", id))

	tree, err := s.referralTreeRepo.GetByIDWithNodes(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("Failed to get referral tree by ID", zap.Error(err))
		return nil, fmt.Errorf("failed to get referral tree by ID: %w", err)
	}

	return tree, nil
}

func (s *AdminAgentReferralService) GetReferralTreeByRootUser(ctx context.Context, rootUserId uuid.UUID) (*ReferralTreeWithNodes, error) {
	global.GVA_LOG.Info("Getting referral tree by root user", zap.String("rootUserId", rootUserId.String()))

	tree, err := s.referralTreeRepo.GetByRootUserWithNodes(ctx, rootUserId)
	if err != nil {
		global.GVA_LOG.Error("Failed to get referral tree by root user", zap.Error(err))
		return nil, fmt.Errorf("failed to get referral tree by root user: %w", err)
	}

	return tree, nil
}

func (s *AdminAgentReferralService) CreateReferralTreeSnapshot(ctx context.Context, input *CreateTreeSnapshotInput) (*CreateTreeSnapshotResult, error) {
	global.GVA_LOG.Info("Creating referral tree snapshot", zap.String("rootUserId", input.RootUserID.String()))

	result, err := s.referralTreeRepo.CreateSnapshot(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to create referral tree snapshot", zap.Error(err))
		return nil, fmt.Errorf("failed to create referral tree snapshot: %w", err)
	}

	global.GVA_LOG.Info("Referral tree snapshot created successfully",
		zap.Uint("treeId", result.TreeID),
		zap.Int("processedNodes", result.ProcessedNodes))
	return result, nil
}

func (s *AdminAgentReferralService) CreateAllReferralTreeSnapshots(ctx context.Context) (*CreateAllTreeSnapshotsResult, error) {
	global.GVA_LOG.Info("Creating all referral tree snapshots")

	result, err := s.referralTreeRepo.CreateAllSnapshots(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to create all referral tree snapshots", zap.Error(err))
		return nil, fmt.Errorf("failed to create all referral tree snapshots: %w", err)
	}

	global.GVA_LOG.Info("All referral tree snapshots created successfully",
		zap.Int("processedTrees", result.ProcessedTrees),
		zap.Int("errorCount", result.ErrorCount),
		zap.Int("totalNodes", result.TotalNodes))
	return result, nil
}

func (s *AdminAgentReferralService) GetInfiniteAgentReferralTrees(ctx context.Context, input *GetInfiniteAgentTreesInput) (*InfiniteAgentTreesResult, error) {
	global.GVA_LOG.Info("Getting infinite agent referral trees",
		zap.Int("page", input.Page),
		zap.Int("pageSize", input.PageSize))

	trees, totalCount, err := s.referralTreeRepo.GetInfiniteAgentTreesWithPagination(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent referral trees", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent referral trees: %w", err)
	}

	totalPages := (totalCount + input.PageSize - 1) / input.PageSize

	return &InfiniteAgentTreesResult{
		Trees:       trees,
		TotalCount:  totalCount,
		CurrentPage: input.Page,
		TotalPages:  totalPages,
	}, nil
}

func (s *AdminAgentReferralService) CreateInfiniteAgentReferralTrees(ctx context.Context, input *CreateInfiniteAgentTreesInput) (*CreateInfiniteAgentTreesResult, error) {
	global.GVA_LOG.Info("Creating infinite agent referral trees",
		zap.Int("agentCount", len(input.InfiniteAgentUserIDs)),
		zap.Bool("forceRecalculate", input.ForceRecalculate))

	result, err := s.referralTreeRepo.CreateInfiniteAgentTrees(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to create infinite agent referral trees", zap.Error(err))
		return nil, fmt.Errorf("failed to create infinite agent referral trees: %w", err)
	}

	global.GVA_LOG.Info("Infinite agent referral trees created successfully",
		zap.Int("processedCount", result.ProcessedCount),
		zap.Int("errorCount", result.ErrorCount),
		zap.String("snapshotDate", result.SnapshotDate))
	return result, nil
}

func (s *AdminAgentReferralService) RecalculateReferralTreeStats(ctx context.Context, treeId uint) (*ReferralTreeWithNodes, error) {
	global.GVA_LOG.Info("Recalculating referral tree stats", zap.Uint("treeId", treeId))

	tree, err := s.referralTreeRepo.RecalculateStats(ctx, treeId)
	if err != nil {
		global.GVA_LOG.Error("Failed to recalculate referral tree stats", zap.Error(err))
		return nil, fmt.Errorf("failed to recalculate referral tree stats: %w", err)
	}

	global.GVA_LOG.Info("Referral tree stats recalculated successfully", zap.Uint("treeId", treeId))
	return tree, nil
}

// ===== USER MANAGEMENT =====

func (s *AdminAgentReferralService) SearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error) {
	global.GVA_LOG.Info("Searching users by invitation code", zap.String("invitationCode", invitationCode))

	users, err := s.userRepo.SearchByInvitationCode(ctx, invitationCode)
	if err != nil {
		global.GVA_LOG.Error("Failed to search users by invitation code", zap.Error(err))
		return nil, fmt.Errorf("failed to search users by invitation code: %w", err)
	}

	return users, nil
}

func (s *AdminAgentReferralService) GetUserReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error) {
	global.GVA_LOG.Info("Getting user referral snapshot", zap.String("userId", userId.String()))

	snapshot, err := s.userRepo.GetReferralSnapshot(ctx, userId)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user referral snapshot", zap.Error(err))
		return nil, fmt.Errorf("failed to get user referral snapshot: %w", err)
	}

	return snapshot, nil
}

func (s *AdminAgentReferralService) GetUserReferralTree(ctx context.Context, userId uuid.UUID, depth int) (*UserReferralTreeResult, error) {
	global.GVA_LOG.Info("Getting user referral tree",
		zap.String("userId", userId.String()),
		zap.Int("depth", depth))

	tree, err := s.userRepo.GetReferralTree(ctx, userId, depth)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user referral tree", zap.Error(err))
		return nil, fmt.Errorf("failed to get user referral tree: %w", err)
	}

	return tree, nil
}

func (s *AdminAgentReferralService) UpdateUserAgentLevel(ctx context.Context, input *UpdateUserAgentLevelInput) (*model.User, error) {
	global.GVA_LOG.Info("Updating user agent level",
		zap.String("userId", input.UserID.String()),
		zap.Int("agentLevelId", input.AgentLevelID))

	user, err := s.userRepo.UpdateAgentLevel(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to update user agent level", zap.Error(err))
		return nil, fmt.Errorf("failed to update user agent level: %w", err)
	}

	global.GVA_LOG.Info("User agent level updated successfully",
		zap.String("userId", user.ID.String()),
		zap.Uint("newAgentLevelId", user.AgentLevelID))
	return user, nil
}

func (s *AdminAgentReferralService) RecalculateUserReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error) {
	global.GVA_LOG.Info("Recalculating user referral snapshot", zap.String("userId", userId.String()))

	snapshot, err := s.userRepo.RecalculateReferralSnapshot(ctx, userId)
	if err != nil {
		global.GVA_LOG.Error("Failed to recalculate user referral snapshot", zap.Error(err))
		return nil, fmt.Errorf("failed to recalculate user referral snapshot: %w", err)
	}

	global.GVA_LOG.Info("User referral snapshot recalculated successfully", zap.String("userId", userId.String()))
	return snapshot, nil
}

func (s *AdminAgentReferralService) CreateUserReferralRelationship(ctx context.Context, input *CreateReferralInput) (*CreateReferralResult, error) {
	global.GVA_LOG.Info("Creating user referral relationship",
		zap.String("userId", input.UserID.String()),
		zap.String("referrerId", input.ReferrerID.String()),
		zap.Int("depth", input.Depth))

	result, err := s.userRepo.CreateReferralRelationship(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to create user referral relationship", zap.Error(err))
		return nil, fmt.Errorf("failed to create user referral relationship: %w", err)
	}

	global.GVA_LOG.Info("User referral relationship created successfully",
		zap.Int("createdRelationships", result.CreatedRelationships))
	return result, nil
}

func (s *AdminAgentReferralService) DeleteUserReferralRelationship(ctx context.Context, input *DeleteReferralInput) error {
	global.GVA_LOG.Info("Deleting user referral relationship",
		zap.String("userId", input.UserID.String()),
		zap.String("referrerId", input.ReferrerID.String()))

	if err := s.userRepo.DeleteReferralRelationship(ctx, input); err != nil {
		global.GVA_LOG.Error("Failed to delete user referral relationship", zap.Error(err))
		return fmt.Errorf("failed to delete user referral relationship: %w", err)
	}

	global.GVA_LOG.Info("User referral relationship deleted successfully")
	return nil
}

// ===== STATISTICS & REPORTS =====

func (s *AdminAgentReferralService) GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (*AgentReferralStatsResult, error) {
	global.GVA_LOG.Info("Getting agent referral stats",
		zap.Time("startDate", startDate),
		zap.Time("endDate", endDate))

	stats, err := s.statisticsRepo.GetAgentReferralStats(ctx, startDate, endDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get agent referral stats", zap.Error(err))
		return nil, fmt.Errorf("failed to get agent referral stats: %w", err)
	}

	return stats, nil
}

func (s *AdminAgentReferralService) GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (*CommissionDistributionResult, error) {
	global.GVA_LOG.Info("Getting commission distribution stats",
		zap.Time("startDate", startDate),
		zap.Time("endDate", endDate))

	stats, err := s.statisticsRepo.GetCommissionDistributionStats(ctx, startDate, endDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get commission distribution stats", zap.Error(err))
		return nil, fmt.Errorf("failed to get commission distribution stats: %w", err)
	}

	return stats, nil
}

func (s *AdminAgentReferralService) GetTopPerformingAgents(ctx context.Context, input *GetTopAgentsInput) (*TopAgentsResult, error) {
	global.GVA_LOG.Info("Getting top performing agents",
		zap.Int("limit", input.Limit),
		zap.String("sortBy", input.SortBy))

	agents, err := s.statisticsRepo.GetTopPerformingAgents(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to get top performing agents", zap.Error(err))
		return nil, fmt.Errorf("failed to get top performing agents: %w", err)
	}

	return agents, nil
}

func (s *AdminAgentReferralService) GetInfiniteAgentPerformanceStats(ctx context.Context, input *GetInfiniteAgentStatsInput) (*InfiniteAgentPerformanceResult, error) {
	global.GVA_LOG.Info("Getting infinite agent performance stats",
		zap.Time("startDate", input.StartDate),
		zap.Time("endDate", input.EndDate))

	stats, err := s.statisticsRepo.GetInfiniteAgentPerformanceStats(ctx, input)
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent performance stats", zap.Error(err))
		return nil, fmt.Errorf("failed to get infinite agent performance stats: %w", err)
	}

	return stats, nil
}

// ===== SYSTEM OPERATIONS =====

func (s *AdminAgentReferralService) RecalculateAllReferralSnapshots(ctx context.Context) (*SystemOperationResult, error) {
	global.GVA_LOG.Info("Recalculating all referral snapshots")

	result, err := s.statisticsRepo.RecalculateAllReferralSnapshots(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to recalculate all referral snapshots", zap.Error(err))
		return nil, fmt.Errorf("failed to recalculate all referral snapshots: %w", err)
	}

	global.GVA_LOG.Info("All referral snapshots recalculated successfully",
		zap.Int("processedCount", result.ProcessedCount),
		zap.Int("errorCount", result.ErrorCount),
		zap.String("duration", result.Duration))
	return result, nil
}

func (s *AdminAgentReferralService) RecalculateAllInfiniteAgentCommissions(ctx context.Context) (*SystemOperationResult, error) {
	global.GVA_LOG.Info("Recalculating all infinite agent commissions")

	result, err := s.statisticsRepo.RecalculateAllInfiniteAgentCommissions(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to recalculate all infinite agent commissions", zap.Error(err))
		return nil, fmt.Errorf("failed to recalculate all infinite agent commissions: %w", err)
	}

	global.GVA_LOG.Info("All infinite agent commissions recalculated successfully",
		zap.Int("processedCount", result.ProcessedCount),
		zap.Int("errorCount", result.ErrorCount),
		zap.String("duration", result.Duration))
	return result, nil
}

func (s *AdminAgentReferralService) SyncReferralTreeData(ctx context.Context) (*SystemOperationResult, error) {
	global.GVA_LOG.Info("Syncing referral tree data")

	result, err := s.statisticsRepo.SyncReferralTreeData(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to sync referral tree data", zap.Error(err))
		return nil, fmt.Errorf("failed to sync referral tree data: %w", err)
	}

	global.GVA_LOG.Info("Referral tree data synced successfully",
		zap.Int("processedCount", result.ProcessedCount),
		zap.Int("errorCount", result.ErrorCount),
		zap.String("duration", result.Duration))
	return result, nil
}
