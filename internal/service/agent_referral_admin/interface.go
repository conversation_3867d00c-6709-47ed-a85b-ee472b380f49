package agent_referral_admin

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// AdminAgentReferralServiceInterface defines the interface for admin agent referral operations
type AdminAgentReferralServiceInterface interface {
	// ===== INFINITE AGENT MANAGEMENT =====
	
	// GetAllInfiniteAgents retrieves all infinite agents with pagination
	GetAllInfiniteAgents(ctx context.Context, page, pageSize int, sortBy, sortOrder string) (*InfiniteAgentsResult, error)
	
	// GetInfiniteAgentById retrieves infinite agent by ID
	GetInfiniteAgentById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	
	// GetInfiniteAgentByUserId retrieves infinite agent by user ID
	GetInfiniteAgentByUserId(ctx context.Context, userId uuid.UUID) (*model.InfiniteAgentConfig, error)
	
	// CreateInfiniteAgent creates a new infinite agent
	CreateInfiniteAgent(ctx context.Context, input *CreateInfiniteAgentInput) (*model.InfiniteAgentConfig, error)
	
	// UpdateInfiniteAgent updates an existing infinite agent
	UpdateInfiniteAgent(ctx context.Context, input *UpdateInfiniteAgentInput) (*model.InfiniteAgentConfig, error)
	
	// DeleteInfiniteAgent deletes an infinite agent
	DeleteInfiniteAgent(ctx context.Context, id uuid.UUID) error
	
	// ToggleInfiniteAgentStatus toggles infinite agent status
	ToggleInfiniteAgentStatus(ctx context.Context, id uuid.UUID, status string) (*model.InfiniteAgentConfig, error)

	// ===== AGENT LEVEL MANAGEMENT =====
	
	// GetAllAgentLevels retrieves all agent levels with statistics
	GetAllAgentLevels(ctx context.Context) ([]*AgentLevelWithStats, error)
	
	// GetAgentLevelById retrieves agent level by ID with statistics
	GetAgentLevelById(ctx context.Context, id int) (*AgentLevelWithStats, error)
	
	// UpdateAgentLevel updates agent level configuration
	UpdateAgentLevel(ctx context.Context, input *UpdateAgentLevelInput) (*model.AgentLevel, error)
	
	// UpdateAgentLevelCommissionRates updates commission rates for an agent level
	UpdateAgentLevelCommissionRates(ctx context.Context, input *UpdateCommissionRatesInput) (*model.AgentLevel, error)
	
	// UpdateAgentLevelVolumeThresholds updates volume thresholds for an agent level
	UpdateAgentLevelVolumeThresholds(ctx context.Context, input *UpdateVolumeThresholdsInput) (*model.AgentLevel, error)
	
	// UpdateAgentLevelFeeRates updates fee rates for an agent level
	UpdateAgentLevelFeeRates(ctx context.Context, input *UpdateFeeRatesInput) (*model.AgentLevel, error)

	// ===== REFERRAL TREE MANAGEMENT =====
	
	// GetAllReferralTrees retrieves all referral trees with pagination and filters
	GetAllReferralTrees(ctx context.Context, input *GetReferralTreesInput) (*ReferralTreesResult, error)
	
	// GetReferralTreeById retrieves referral tree by ID
	GetReferralTreeById(ctx context.Context, id uint) (*ReferralTreeWithNodes, error)
	
	// GetReferralTreeByRootUser retrieves referral tree by root user ID
	GetReferralTreeByRootUser(ctx context.Context, rootUserId uuid.UUID) (*ReferralTreeWithNodes, error)
	
	// CreateReferralTreeSnapshot creates a new referral tree snapshot
	CreateReferralTreeSnapshot(ctx context.Context, input *CreateTreeSnapshotInput) (*CreateTreeSnapshotResult, error)
	
	// CreateAllReferralTreeSnapshots creates snapshots for all root users
	CreateAllReferralTreeSnapshots(ctx context.Context) (*CreateAllTreeSnapshotsResult, error)
	
	// GetInfiniteAgentReferralTrees retrieves infinite agent referral trees
	GetInfiniteAgentReferralTrees(ctx context.Context, input *GetInfiniteAgentTreesInput) (*InfiniteAgentTreesResult, error)
	
	// CreateInfiniteAgentReferralTrees creates infinite agent referral trees
	CreateInfiniteAgentReferralTrees(ctx context.Context, input *CreateInfiniteAgentTreesInput) (*CreateInfiniteAgentTreesResult, error)
	
	// RecalculateReferralTreeStats recalculates statistics for a referral tree
	RecalculateReferralTreeStats(ctx context.Context, treeId uint) (*ReferralTreeWithNodes, error)

	// ===== USER MANAGEMENT =====
	
	// SearchUsersByInvitationCode searches users by invitation code
	SearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error)
	
	// GetUserReferralSnapshot retrieves user referral snapshot
	GetUserReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error)
	
	// GetUserReferralTree retrieves user referral tree with specified depth
	GetUserReferralTree(ctx context.Context, userId uuid.UUID, depth int) (*UserReferralTreeResult, error)
	
	// UpdateUserAgentLevel updates user agent level
	UpdateUserAgentLevel(ctx context.Context, input *UpdateUserAgentLevelInput) (*model.User, error)
	
	// RecalculateUserReferralSnapshot recalculates user referral snapshot
	RecalculateUserReferralSnapshot(ctx context.Context, userId uuid.UUID) (*model.ReferralSnapshot, error)
	
	// CreateUserReferralRelationship creates a referral relationship
	CreateUserReferralRelationship(ctx context.Context, input *CreateReferralInput) (*CreateReferralResult, error)
	
	// DeleteUserReferralRelationship deletes a referral relationship
	DeleteUserReferralRelationship(ctx context.Context, input *DeleteReferralInput) error

	// ===== STATISTICS & REPORTS =====
	
	// GetAgentReferralStats retrieves overall agent referral statistics
	GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (*AgentReferralStatsResult, error)
	
	// GetCommissionDistributionStats retrieves commission distribution statistics
	GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (*CommissionDistributionResult, error)
	
	// GetTopPerformingAgents retrieves top performing agents
	GetTopPerformingAgents(ctx context.Context, input *GetTopAgentsInput) (*TopAgentsResult, error)
	
	// GetInfiniteAgentPerformanceStats retrieves infinite agent performance statistics
	GetInfiniteAgentPerformanceStats(ctx context.Context, input *GetInfiniteAgentStatsInput) (*InfiniteAgentPerformanceResult, error)

	// ===== SYSTEM OPERATIONS =====
	
	// RecalculateAllReferralSnapshots recalculates all user referral snapshots
	RecalculateAllReferralSnapshots(ctx context.Context) (*SystemOperationResult, error)
	
	// RecalculateAllInfiniteAgentCommissions recalculates all infinite agent commissions
	RecalculateAllInfiniteAgentCommissions(ctx context.Context) (*SystemOperationResult, error)
	
	// SyncReferralTreeData synchronizes referral tree data
	SyncReferralTreeData(ctx context.Context) (*SystemOperationResult, error)
}

// ===== INPUT TYPES =====

type CreateInfiniteAgentInput struct {
	UserID          uuid.UUID
	CommissionRateN float64
	Status          string
	Description     *string
}

type UpdateInfiniteAgentInput struct {
	ID              uuid.UUID
	CommissionRateN *float64
	Status          *string
	Description     *string
}

type UpdateAgentLevelInput struct {
	ID                      int
	Name                    *string
	MemeVolumeThreshold     *float64
	ContractVolumeThreshold *float64
	MemeFeeRate             *float64
	TakerFeeRate            *float64
	MakerFeeRate            *float64
	DirectCommissionRate    *float64
	IndirectCommissionRate  *float64
	ExtendedCommissionRate  *float64
	MemeFeeRebate           *float64
}

type UpdateCommissionRatesInput struct {
	LevelID                int
	DirectCommissionRate   float64
	IndirectCommissionRate float64
	ExtendedCommissionRate float64
}

type UpdateVolumeThresholdsInput struct {
	LevelID                 int
	MemeVolumeThreshold     float64
	ContractVolumeThreshold float64
}

type UpdateFeeRatesInput struct {
	LevelID       int
	MemeFeeRate   float64
	TakerFeeRate  float64
	MakerFeeRate  float64
	MemeFeeRebate float64
}

type GetReferralTreesInput struct {
	Page             int
	PageSize         int
	RootUserID       *uuid.UUID
	HasInfiniteAgent *bool
	StartDate        *time.Time
	EndDate          *time.Time
	SortBy           string
	SortOrder        string
}

type CreateTreeSnapshotInput struct {
	RootUserID   uuid.UUID
	SnapshotDate *time.Time
	Description  *string
}

type GetInfiniteAgentTreesInput struct {
	Page                 int
	PageSize             int
	InfiniteAgentUserID  *uuid.UUID
	StartDate            *time.Time
	EndDate              *time.Time
	SortBy               string
	SortOrder            string
}

type CreateInfiniteAgentTreesInput struct {
	InfiniteAgentUserIDs []uuid.UUID
	SnapshotDate         *time.Time
	ForceRecalculate     bool
}

type UpdateUserAgentLevelInput struct {
	UserID       uuid.UUID
	AgentLevelID int
	Reason       *string
}

type CreateReferralInput struct {
	UserID     uuid.UUID
	ReferrerID uuid.UUID
	Depth      int
}

type DeleteReferralInput struct {
	UserID     uuid.UUID
	ReferrerID uuid.UUID
}

type GetTopAgentsInput struct {
	Limit        int
	SortBy       string
	StartDate    *time.Time
	EndDate      *time.Time
	AgentLevelID *int
}

type GetInfiniteAgentStatsInput struct {
	InfiniteAgentUserID   *uuid.UUID
	StartDate             time.Time
	EndDate               time.Time
	IncludeTreeBreakdown  bool
}

// ===== RESULT TYPES =====

type InfiniteAgentsResult struct {
	InfiniteAgents []*model.InfiniteAgentConfig
	TotalCount     int
	CurrentPage    int
	TotalPages     int
}

type AgentLevelWithStats struct {
	*model.AgentLevel
	UserCount             int
	ActiveUserCount       int
	TotalVolumeUSD        float64
	TotalCommissionPaidUSD float64
}

type ReferralTreesResult struct {
	Trees       []*ReferralTreeWithNodes
	TotalCount  int
	CurrentPage int
	TotalPages  int
}

type ReferralTreeWithNodes struct {
	*model.ReferralTreeSnapshot
	Nodes                  []*model.ReferralTreeNode
	TotalVolumeUSD         float64
	TotalCommissionPaidUSD float64
}

type CreateTreeSnapshotResult struct {
	TreeID         uint
	ProcessedNodes int
}

type CreateAllTreeSnapshotsResult struct {
	ProcessedTrees int
	ErrorCount     int
	TotalNodes     int
}

type InfiniteAgentTreesResult struct {
	Trees       []*model.InfiniteAgentReferralTree
	TotalCount  int
	CurrentPage int
	TotalPages  int
}

type CreateInfiniteAgentTreesResult struct {
	ProcessedCount        int
	ErrorCount            int
	TotalInfiniteAgents   int
	SnapshotDate          string
}

type UserReferralTreeResult struct {
	UserID                   uuid.UUID
	User                     *model.User
	ReferralTree             []*UserReferralNode
	TotalNodes               int
	MaxDepth                 int
	DirectCount              int
	ActiveUsers              int
	TradingUsers             int
	TotalVolumeUSD           float64
	TotalCommissionEarnedUSD float64
}

type UserReferralNode struct {
	UserID                   uuid.UUID
	User                     *model.User
	ParentUserID             *uuid.UUID
	ParentUser               *model.User
	Depth                    int
	Level                    int
	IsActive                 bool
	IsTrading                bool
	AgentLevelID             int
	AgentLevel               *model.AgentLevel
	DirectReferrals          int
	TotalDownline            int
	VolumeUSD                float64
	CommissionEarnedUSD      float64
	Children                 []*UserReferralNode
}

type CreateReferralResult struct {
	CreatedRelationships int
}

type AgentReferralStatsResult struct {
	TotalUsers              int
	TotalActiveUsers        int
	TotalTradingUsers       int
	TotalInfiniteAgents     int
	TotalReferralTrees      int
	TotalVolumeUSD          float64
	TotalCommissionPaidUSD  float64
	TotalCashbackPaidUSD    float64
	AverageTreeDepth        float64
	AverageDirectReferrals  float64
	LevelDistribution       []*LevelDistribution
	DailyStats              []*DailyAgentStats
}

type LevelDistribution struct {
	LevelID                int
	LevelName              string
	UserCount              int
	Percentage             float64
	TotalVolumeUSD         float64
	TotalCommissionPaidUSD float64
}

type DailyAgentStats struct {
	Date                string
	NewUsers            int
	ActiveUsers         int
	TradingUsers        int
	VolumeUSD           float64
	CommissionPaidUSD   float64
	CashbackPaidUSD     float64
}

type CommissionDistributionResult struct {
	TotalCommissionPaidUSD     float64
	DirectCommissionUSD        float64
	IndirectCommissionUSD      float64
	ExtendedCommissionUSD      float64
	InfiniteAgentCommissionUSD float64
	LevelBreakdown             []*CommissionLevelBreakdown
	DailyDistribution          []*DailyCommissionStats
}

type CommissionLevelBreakdown struct {
	LevelID               int
	LevelName             string
	TotalCommissionUSD    float64
	DirectCommissionUSD   float64
	IndirectCommissionUSD float64
	ExtendedCommissionUSD float64
	UserCount             int
}

type DailyCommissionStats struct {
	Date                       string
	TotalCommissionUSD         float64
	DirectCommissionUSD        float64
	IndirectCommissionUSD      float64
	ExtendedCommissionUSD      float64
	InfiniteAgentCommissionUSD float64
}

type TopAgentsResult struct {
	Agents     []*TopAgent
	TotalCount int
}

type TopAgent struct {
	UserID                   uuid.UUID
	User                     *model.User
	AgentLevelID             int
	AgentLevel               *model.AgentLevel
	TotalVolumeUSD           float64
	TotalCommissionEarnedUSD float64
	DirectReferrals          int
	TotalDownline            int
	TradingUsers             int
	Rank                     int
	IsInfiniteAgent          bool
}

type InfiniteAgentPerformanceResult struct {
	InfiniteAgentUserID            *uuid.UUID
	InfiniteAgentUser              *model.User
	TotalCommissionEarnedUSD       float64
	TotalNetFeeUSD                 float64
	TotalStandardCommissionPaidUSD float64
	FinalCommissionAmountUSD       float64
	CommissionRateN                float64
	TreeCount                      int
	TotalNodes                     int
	ActiveUsers                    int
	TradingUsers                   int
	TotalVolumeUSD                 float64
	MemeBreakdown                  *InfiniteAgentMemeBreakdown
	ContractBreakdown              *InfiniteAgentContractBreakdown
	DailyPerformance               []*DailyInfiniteAgentStats
	TreeBreakdown                  []*InfiniteAgentTreeBreakdown
}

type InfiniteAgentMemeBreakdown struct {
	TotalFeeUSD         float64
	PaidCommissionUSD   float64
	NetFeeUSD           float64
	ActivityCashbackUSD float64
	TransactionCount    int
	VolumeUSD           float64
}

type InfiniteAgentContractBreakdown struct {
	TotalFeeUSD       float64
	PaidCommissionUSD float64
	NetFeeUSD         float64
	TransactionCount  int
	VolumeUSD         float64
}

type DailyInfiniteAgentStats struct {
	Date                 string
	CommissionEarnedUSD  float64
	NetFeeUSD            float64
	VolumeUSD            float64
	ActiveUsers          int
	TradingUsers         int
	NewNodes             int
}

type InfiniteAgentTreeBreakdown struct {
	TreeID               uint
	RootUserID           uuid.UUID
	RootUser             *model.User
	TotalNodes           int
	ActiveUsers          int
	TradingUsers         int
	VolumeUSD            float64
	CommissionEarnedUSD  float64
	NetFeeUSD            float64
	SnapshotDate         time.Time
}

type SystemOperationResult struct {
	ProcessedCount int
	ErrorCount     int
	Duration       string
}
