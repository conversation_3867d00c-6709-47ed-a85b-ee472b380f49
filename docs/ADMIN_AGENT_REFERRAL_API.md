# Admin Agent Referral API Documentation

## Overview

This document describes the Admin APIs for managing the Agent Referral system in xbit-dex. These APIs provide comprehensive management capabilities for infinite agents, agent levels, referral trees, and related statistics.

## Authentication

All admin APIs require authentication using the `x-api-key` header with a valid admin API key.

```
x-api-key: your-admin-api-key
```

## GraphQL Endpoint

```
POST /admin/graphql
```

## API Categories

### 1. Infinite Agent Management

#### Query: Get All Infinite Agents
```graphql
query GetAllInfiniteAgents($input: AdminPaginationInput) {
  adminGetAllInfiniteAgents(input: $input) {
    infiniteAgents {
      id
      userId
      user {
        id
        email
        invitationCode
      }
      commissionRateN
      totalNetFeeUsd
      totalStandardCommissionPaidUsd
      finalCommissionAmountUsd
      status
      createdAt
      updatedAt
    }
    totalCount
    currentPage
    totalPages
    success
    message
  }
}
```

#### Query: Get Infinite Agent by ID
```graphql
query GetInfiniteAgentById($id: ID!) {
  adminGetInfiniteAgentById(id: $id) {
    infiniteAgent {
      id
      userId
      commissionRateN
      status
      # ... other fields
    }
    success
    message
  }
}
```

#### Mutation: Create Infinite Agent
```graphql
mutation CreateInfiniteAgent($input: AdminCreateInfiniteAgentInput!) {
  adminCreateInfiniteAgent(input: $input) {
    infiniteAgent {
      id
      userId
      commissionRateN
      status
    }
    success
    message
  }
}
```

#### Mutation: Update Infinite Agent
```graphql
mutation UpdateInfiniteAgent($input: AdminUpdateInfiniteAgentInput!) {
  adminUpdateInfiniteAgent(input: $input) {
    infiniteAgent {
      id
      commissionRateN
      status
    }
    success
    message
  }
}
```

#### Mutation: Delete Infinite Agent
```graphql
mutation DeleteInfiniteAgent($id: ID!) {
  adminDeleteInfiniteAgent(id: $id) {
    success
    message
  }
}
```

### 2. Agent Level Management

#### Query: Get All Agent Levels
```graphql
query GetAllAgentLevels {
  adminGetAllAgentLevels {
    id
    name
    memeVolumeThreshold
    contractVolumeThreshold
    memeFeeRate
    takerFeeRate
    makerFeeRate
    directCommissionRate
    indirectCommissionRate
    extendedCommissionRate
    memeFeeRebate
    userCount
    activeUserCount
    totalVolumeUsd
    totalCommissionPaidUsd
  }
}
```

#### Query: Get Agent Level by ID
```graphql
query GetAgentLevelById($id: Int!) {
  adminGetAgentLevelById(id: $id) {
    id
    name
    # ... other fields
  }
}
```

#### Mutation: Update Agent Level Commission Rates
```graphql
mutation UpdateAgentLevelCommissionRates($input: AdminUpdateCommissionRatesInput!) {
  adminUpdateAgentLevelCommissionRates(input: $input) {
    agentLevel {
      id
      directCommissionRate
      indirectCommissionRate
      extendedCommissionRate
    }
    success
    message
  }
}
```

### 3. Referral Tree Management

#### Query: Get All Referral Trees
```graphql
query GetAllReferralTrees($input: AdminReferralTreesInput!) {
  adminGetAllReferralTrees(input: $input) {
    trees {
      id
      rootUserId
      rootUser {
        id
        email
        invitationCode
      }
      snapshotDate
      totalNodes
      maxDepth
      directCount
      activeUsers
      tradingUsers
      hasInfiniteAgent
    }
    totalCount
    currentPage
    totalPages
    success
    message
  }
}
```

#### Mutation: Create Referral Tree Snapshot
```graphql
mutation CreateReferralTreeSnapshot($input: AdminCreateTreeSnapshotInput!) {
  adminCreateReferralTreeSnapshot(input: $input) {
    success
    message
    treeId
    processedNodes
  }
}
```

### 4. User Management

#### Query: Search Users by Invitation Code
```graphql
query SearchUsersByInvitationCode($invitationCode: String!) {
  adminSearchUsersByInvitationCode(invitationCode: $invitationCode) {
    id
    email
    invitationCode
    agentLevelId
    agentLevel {
      id
      name
    }
    createdAt
    firstTransactionAt
  }
}
```

#### Query: Get User Referral Snapshot
```graphql
query GetUserReferralSnapshot($userId: ID!) {
  adminGetUserReferralSnapshot(userId: $userId) {
    snapshot {
      userId
      directCount
      totalDownlineCount
      tradingUserCount
      totalVolumeUsd
      totalCommissionEarnedUsd
      # ... other fields
    }
    success
    message
  }
}
```

#### Mutation: Update User Agent Level
```graphql
mutation UpdateUserAgentLevel($input: AdminUpdateUserAgentLevelInput!) {
  adminUpdateUserAgentLevel(input: $input) {
    user {
      id
      agentLevelId
      agentLevel {
        id
        name
      }
    }
    success
    message
  }
}
```

### 5. Statistics & Reports

#### Query: Get Agent Referral Statistics
```graphql
query GetAgentReferralStats($input: AdminStatsInput!) {
  adminGetAgentReferralStats(input: $input) {
    totalUsers
    totalActiveUsers
    totalTradingUsers
    totalInfiniteAgents
    totalReferralTrees
    totalVolumeUsd
    totalCommissionPaidUsd
    totalCashbackPaidUsd
    averageTreeDepth
    averageDirectReferrals
    levelDistribution {
      levelId
      levelName
      userCount
      percentage
      totalVolumeUsd
      totalCommissionPaidUsd
    }
    success
    message
  }
}
```

#### Query: Get Top Performing Agents
```graphql
query GetTopPerformingAgents($input: AdminTopAgentsInput!) {
  adminGetTopPerformingAgents(input: $input) {
    agents {
      userId
      user {
        id
        email
        invitationCode
      }
      agentLevel {
        id
        name
      }
      totalVolumeUsd
      totalCommissionEarnedUsd
      directReferrals
      totalDownline
      tradingUsers
      rank
      isInfiniteAgent
    }
    totalCount
    success
    message
  }
}
```

### 6. System Operations

#### Mutation: Recalculate All Referral Snapshots
```graphql
mutation RecalculateAllReferralSnapshots {
  adminRecalculateAllReferralSnapshots {
    success
    message
    processedCount
    errorCount
    duration
  }
}
```

#### Mutation: Recalculate All Infinite Agent Commissions
```graphql
mutation RecalculateAllInfiniteAgentCommissions {
  adminRecalculateAllInfiniteAgentCommissions {
    success
    message
    processedCount
    errorCount
    duration
  }
}
```

## Input Types

### AdminPaginationInput
```graphql
input AdminPaginationInput {
  page: Int = 1
  pageSize: Int = 20
  sortBy: String
  sortOrder: SortOrder = DESC
}
```

### AdminCreateInfiniteAgentInput
```graphql
input AdminCreateInfiniteAgentInput {
  userId: ID!
  commissionRateN: Float!
  status: InfiniteAgentStatus = ACTIVE
  description: String
}
```

### AdminUpdateInfiniteAgentInput
```graphql
input AdminUpdateInfiniteAgentInput {
  id: ID!
  commissionRateN: Float
  status: InfiniteAgentStatus
  description: String
}
```

### AdminUpdateCommissionRatesInput
```graphql
input AdminUpdateCommissionRatesInput {
  levelId: Int!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
}
```

## Enums

### InfiniteAgentStatus
```graphql
enum InfiniteAgentStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}
```

### SortOrder
```graphql
enum SortOrder {
  ASC
  DESC
}
```

## Error Handling

All APIs return a consistent response format with `success` and `message` fields:

```json
{
  "success": false,
  "message": "Error description here"
}
```

Common error scenarios:
- Invalid API key: 401 Unauthorized
- Invalid input parameters: 400 Bad Request with validation details
- Resource not found: 404 Not Found
- Internal server error: 500 Internal Server Error

## Rate Limiting

Admin APIs are subject to rate limiting:
- 1000 requests per minute per API key
- Burst limit of 100 requests per 10 seconds

## Examples

### Create an Infinite Agent
```bash
curl -X POST http://localhost:8080/admin/graphql \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-admin-api-key" \
  -d '{
    "query": "mutation CreateInfiniteAgent($input: AdminCreateInfiniteAgentInput!) { adminCreateInfiniteAgent(input: $input) { infiniteAgent { id userId commissionRateN status } success message } }",
    "variables": {
      "input": {
        "userId": "123e4567-e89b-12d3-a456-426614174000",
        "commissionRateN": 0.15,
        "status": "ACTIVE"
      }
    }
  }'
```

### Get Agent Level Statistics
```bash
curl -X POST http://localhost:8080/admin/graphql \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-admin-api-key" \
  -d '{
    "query": "query GetAllAgentLevels { adminGetAllAgentLevels { id name userCount activeUserCount totalVolumeUsd totalCommissionPaidUsd } }"
  }'
```

## Notes

- All timestamps are in UTC format
- Decimal values are returned as floats in GraphQL responses
- User IDs are UUIDs in string format
- Commission rates are decimal values (e.g., 0.15 = 15%)
- Volume and commission amounts are in USD
