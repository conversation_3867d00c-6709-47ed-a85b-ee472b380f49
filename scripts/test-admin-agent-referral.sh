#!/bin/bash

# Test script for Admin Agent Referral APIs
# Usage: ./scripts/test-admin-agent-referral.sh [API_KEY] [BASE_URL]

set -e

# Configuration
API_KEY=${1:-"your-admin-api-key"}
BASE_URL=${2:-"http://localhost:8080"}
GRAPHQL_ENDPOINT="${BASE_URL}/admin/graphql"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper function to make GraphQL requests
make_graphql_request() {
    local query="$1"
    local variables="$2"
    local description="$3"
    
    echo -e "${BLUE}Testing: ${description}${NC}"
    
    local payload
    if [ -n "$variables" ]; then
        payload=$(jq -n --arg query "$query" --argjson variables "$variables" '{query: $query, variables: $variables}')
    else
        payload=$(jq -n --arg query "$query" '{query: $query}')
    fi
    
    local response
    response=$(curl -s -X POST "$GRAPHQL_ENDPOINT" \
        -H "Content-Type: application/json" \
        -H "x-api-key: $API_KEY" \
        -d "$payload")
    
    # Check if response contains errors
    if echo "$response" | jq -e '.errors' > /dev/null 2>&1; then
        echo -e "${RED}❌ Error:${NC}"
        echo "$response" | jq '.errors'
        return 1
    else
        echo -e "${GREEN}✅ Success${NC}"
        echo "$response" | jq '.'
        return 0
    fi
}

# Test functions

test_get_all_agent_levels() {
    local query='
    query GetAllAgentLevels {
        adminGetAllAgentLevels {
            id
            name
            memeVolumeThreshold
            contractVolumeThreshold
            memeFeeRate
            directCommissionRate
            indirectCommissionRate
            extendedCommissionRate
            userCount
            activeUserCount
            totalVolumeUsd
            totalCommissionPaidUsd
        }
    }'
    
    make_graphql_request "$query" "" "Get All Agent Levels"
}

test_get_all_infinite_agents() {
    local query='
    query GetAllInfiniteAgents($input: AdminPaginationInput) {
        adminGetAllInfiniteAgents(input: $input) {
            infiniteAgents {
                id
                userId
                commissionRateN
                totalNetFeeUsd
                totalStandardCommissionPaidUsd
                finalCommissionAmountUsd
                status
                createdAt
                updatedAt
            }
            totalCount
            currentPage
            totalPages
            success
            message
        }
    }'
    
    local variables='{"input": {"page": 1, "pageSize": 10}}'
    make_graphql_request "$query" "$variables" "Get All Infinite Agents"
}

test_search_users_by_invitation_code() {
    local query='
    query SearchUsersByInvitationCode($invitationCode: String!) {
        adminSearchUsersByInvitationCode(invitationCode: $invitationCode) {
            id
            email
            invitationCode
            agentLevelId
            agentLevel {
                id
                name
            }
            createdAt
            firstTransactionAt
        }
    }'
    
    local variables='{"invitationCode": "test"}'
    make_graphql_request "$query" "$variables" "Search Users by Invitation Code"
}

test_get_agent_referral_stats() {
    local query='
    query GetAgentReferralStats($input: AdminStatsInput!) {
        adminGetAgentReferralStats(input: $input) {
            totalUsers
            totalActiveUsers
            totalTradingUsers
            totalInfiniteAgents
            totalReferralTrees
            totalVolumeUsd
            totalCommissionPaidUsd
            totalCashbackPaidUsd
            averageTreeDepth
            averageDirectReferrals
            levelDistribution {
                levelId
                levelName
                userCount
                percentage
                totalVolumeUsd
                totalCommissionPaidUsd
            }
            success
            message
        }
    }'
    
    local start_date=$(date -d "30 days ago" -Iseconds)
    local end_date=$(date -Iseconds)
    local variables="{\"input\": {\"startDate\": \"$start_date\", \"endDate\": \"$end_date\"}}"
    
    make_graphql_request "$query" "$variables" "Get Agent Referral Statistics"
}

test_create_infinite_agent() {
    # Generate a random UUID for testing
    local user_id=$(uuidgen)
    
    local query='
    mutation CreateInfiniteAgent($input: AdminCreateInfiniteAgentInput!) {
        adminCreateInfiniteAgent(input: $input) {
            infiniteAgent {
                id
                userId
                commissionRateN
                status
                createdAt
            }
            success
            message
        }
    }'
    
    local variables="{\"input\": {\"userId\": \"$user_id\", \"commissionRateN\": 0.15, \"status\": \"ACTIVE\"}}"
    make_graphql_request "$query" "$variables" "Create Infinite Agent"
}

test_update_agent_level_commission_rates() {
    local query='
    mutation UpdateAgentLevelCommissionRates($input: AdminUpdateCommissionRatesInput!) {
        adminUpdateAgentLevelCommissionRates(input: $input) {
            agentLevel {
                id
                directCommissionRate
                indirectCommissionRate
                extendedCommissionRate
            }
            success
            message
        }
    }'
    
    local variables='{"input": {"levelId": 1, "directCommissionRate": 0.12, "indirectCommissionRate": 0.05, "extendedCommissionRate": 0.02}}'
    make_graphql_request "$query" "$variables" "Update Agent Level Commission Rates"
}

test_recalculate_all_referral_snapshots() {
    local query='
    mutation RecalculateAllReferralSnapshots {
        adminRecalculateAllReferralSnapshots {
            success
            message
            processedCount
            errorCount
            duration
        }
    }'
    
    make_graphql_request "$query" "" "Recalculate All Referral Snapshots"
}

# Main test execution
main() {
    echo -e "${YELLOW}🚀 Starting Admin Agent Referral API Tests${NC}"
    echo -e "${YELLOW}API Key: ${API_KEY}${NC}"
    echo -e "${YELLOW}Base URL: ${BASE_URL}${NC}"
    echo -e "${YELLOW}GraphQL Endpoint: ${GRAPHQL_ENDPOINT}${NC}"
    echo ""
    
    # Check if server is running
    if ! curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${RED}❌ Server is not running at ${BASE_URL}${NC}"
        echo "Please start the server first"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Server is running${NC}"
    echo ""
    
    # Test counter
    local total_tests=0
    local passed_tests=0
    
    # Run tests
    echo -e "${YELLOW}📊 Testing Query Operations${NC}"
    echo "=================================="
    
    if test_get_all_agent_levels; then
        ((passed_tests++))
    fi
    ((total_tests++))
    echo ""
    
    if test_get_all_infinite_agents; then
        ((passed_tests++))
    fi
    ((total_tests++))
    echo ""
    
    if test_search_users_by_invitation_code; then
        ((passed_tests++))
    fi
    ((total_tests++))
    echo ""
    
    if test_get_agent_referral_stats; then
        ((passed_tests++))
    fi
    ((total_tests++))
    echo ""
    
    echo -e "${YELLOW}🔧 Testing Mutation Operations${NC}"
    echo "=================================="
    
    if test_create_infinite_agent; then
        ((passed_tests++))
    fi
    ((total_tests++))
    echo ""
    
    if test_update_agent_level_commission_rates; then
        ((passed_tests++))
    fi
    ((total_tests++))
    echo ""
    
    if test_recalculate_all_referral_snapshots; then
        ((passed_tests++))
    fi
    ((total_tests++))
    echo ""
    
    # Summary
    echo -e "${YELLOW}📋 Test Summary${NC}"
    echo "=================================="
    echo -e "Total Tests: ${total_tests}"
    echo -e "Passed: ${GREEN}${passed_tests}${NC}"
    echo -e "Failed: ${RED}$((total_tests - passed_tests))${NC}"
    
    if [ $passed_tests -eq $total_tests ]; then
        echo -e "${GREEN}🎉 All tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}❌ Some tests failed${NC}"
        exit 1
    fi
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if ! command -v uuidgen &> /dev/null; then
        missing_deps+=("uuidgen")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo -e "${RED}❌ Missing dependencies: ${missing_deps[*]}${NC}"
        echo "Please install the missing dependencies and try again"
        exit 1
    fi
}

# Help function
show_help() {
    echo "Usage: $0 [API_KEY] [BASE_URL]"
    echo ""
    echo "Test script for Admin Agent Referral APIs"
    echo ""
    echo "Arguments:"
    echo "  API_KEY   Admin API key (default: 'your-admin-api-key')"
    echo "  BASE_URL  Base URL of the server (default: 'http://localhost:8080')"
    echo ""
    echo "Examples:"
    echo "  $0"
    echo "  $0 my-secret-key"
    echo "  $0 my-secret-key http://localhost:3000"
    echo ""
    echo "Dependencies:"
    echo "  - curl"
    echo "  - jq"
    echo "  - uuidgen"
}

# Parse command line arguments
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run the tests
check_dependencies
main
